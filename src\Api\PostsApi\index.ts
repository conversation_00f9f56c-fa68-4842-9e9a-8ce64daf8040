// @ts-nocheck
import { type AxiosInstance } from 'axios'

import { SIGNINURL } from '../../Constants/apiConstants'

export default class UserApi {
  private readonly interceptor: AxiosInstance | null
  constructor(interceptor: AxiosInstance | null) {
    this.interceptor = interceptor as AxiosInstance
  }

  public async createPost(payload: any) {
    return await new Promise((resolve, reject) => {
      try {
        this.interceptor
          ?.post(SIGNINURL, payload)
          .then(r => {
            resolve(r.data)
          })
          .catch(e => {
            reject(e)
          })
      } catch (e) {
        reject(e)
      }
    })
  }
}
