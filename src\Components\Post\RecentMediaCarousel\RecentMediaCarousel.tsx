import React, { useState } from 'react';
import Carousel from 'react-simply-carousel';
import './RecentMediaCarousel.scss';

interface RecentMediaCarouselProps {
  posts: any[];
  setModalShow: (show: boolean) => void;
  setActivePost: (post: any) => void;
}

const RecentMediaCarousel: React.FC<RecentMediaCarouselProps> = ({ posts, setModalShow, setActivePost }) => {
  const [activeSlide, setActiveSlide] = useState(0);

  // Filter posts to get only those with media
  const postsWithMedia = posts?.filter(post => 
    post?.medias?.length > 0 && !post.isPinned
  );

  // Get all media items from posts
  const allMedia = postsWithMedia?.reduce((acc: any[], post) => {
    const mediaItems = post.medias.map((media: any) => ({
      ...media,
      postId: post._id,
      post: post
    }));
    return [...acc, ...mediaItems];
  }, []);

  // Take only the most recent 10 media items
  const recentMedia = allMedia.slice(0, 10);

  const responsive = [
    { minWidth: 1024.9, itemsToShow: 6 },
    { minWidth: 768, itemsToShow: 4 },
    { maxWidth: 767, itemsToShow: 3 },
  ];

  if (!recentMedia.length) return null;

  return (
    <div className="recent-media-carousel">
      <h4>Recent Media</h4>
      <Carousel
        containerProps={{
          style: {
            width: '100%',
            justifyContent: 'space-between',
            userSelect: 'text',
          },
          className: 'media-carousel-wrapper',
        }}
        infinite={false}
        activeSlideIndex={activeSlide}
        onRequestChange={setActiveSlide}
        forwardBtnProps={{
          children: <i className="fa fa-arrow-right"></i>,
          className: 'control next',
        }}
        backwardBtnProps={{
          children: <i className="fa fa-arrow-left"></i>,
          className: 'control prev',
        }}
        dotsNav={{
          show: false,
        }}
        speed={100}
        responsiveProps={responsive}
        preventScrollOnSwipe={true}
      >
        {recentMedia.map((media: any, index: number) => (
          <div
            key={index}
            className="media-item"
            onClick={() => {
              setModalShow(true);
              setActivePost({
                ...media.post,
                activeIndex: media.post.medias.findIndex((m: any) => m._id === media._id)
              });
            }}
          >
            {media.type === 'video' ? (
              <div className="video-thumbnail">
                <img src={media.thumbnail || media.url} alt="Video thumbnail" />
                <div className="play-icon">
                  <i className="fa fa-play"></i>
                </div>
              </div>
            ) : media.type === 'audio' ? (
              <div className="audio-thumbnail">
                <i className="fa fa-butterflyheadphone"></i>
              </div>
            ) : (
              <img src={media.url} alt="Media content" />
            )}
          </div>
        ))}
      </Carousel>
    </div>
  );
};

export default RecentMediaCarousel; 