
interface Question {
  _id: string;
  question: string;
  firstPerson: string;
  is_deleted: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export const defaultQuestions: Question[] = [
  {
    _id: '1',
    question: 'Who was the biggest influence in your life?',
    first<PERSON>erson: 'Who was the biggest influence in my life?',
    is_deleted: false
  },
  {
    _id: '2',
    question: 'What major event or realization shaped who you are?',
    first<PERSON>erson: 'What major event or realization shaped who I am?',
    is_deleted: false
  },
  {
    _id: '3',
    question: 'Who was your closest friend growing up?',
    first<PERSON>erson: 'Who was my closest friend growing up?',
    is_deleted: false
  },
  {
    _id: '4',
    question: 'What was the most important lesson you learned as a parent?',
    first<PERSON>erson: 'What was the most important lesson I learned as a parent?',
    is_deleted: false
  },
  {
    _id: '5',
    question: 'What is the best advice you could give your child?',
    first<PERSON>erson: 'What is the best advice I could give my child?',
    is_deleted: false
  },
  {
    _id: '6',
    question: 'What phrase has kept you afloat during hard times?',
    firstPerson: 'What phrase has kept me afloat during hard times?',
    is_deleted: false
  },
  {
    _id: '7',
    question: 'What advice do you have about growing older?',
    firstPerson: 'What advice do I have about growing older?',
    is_deleted: false
  },
  {
    _id: '8',
    question: 'What are some of your favorite memories growing up?',
    firstPerson: 'What are some of my favorite memories growing up?',
    is_deleted: false
  },
  {
    _id: '9',
    question: 'When life was difficult, what did you do to overcome it?',
    firstPerson: 'When life was difficult, what did I do to overcome it?',
    is_deleted: false
  },
  {
    _id: '10',
    question: 'What is one thing you haven\'t done you regret?',
    firstPerson: 'What is one thing I haven\'t done I regret?',
    is_deleted: false
  },
  {
    _id: '11',
    question: 'What are you most proud of having done in life?',
    firstPerson: 'What am I most proud of having done in life?',
    is_deleted: false
  },
  {
    _id: '12',
    question: 'Do you have any superstitions?',
    firstPerson: 'Do I have any superstitions?',
    is_deleted: false
  },
  {
    _id: '13',
    question: 'What do you hold sacred?',
    firstPerson: 'What do I hold sacred?',
    is_deleted: false
  },
  {
    _id: '14',
    question: 'Did you have a nickname? What is the story behind it?',
    firstPerson: 'Did I have a nickname? What is the story behind it?',
    is_deleted: false
  },
  {
    _id: '15',
    question: 'If you could redo any period in your life, what would it be and why?',
    firstPerson: 'If I could redo any period in my life, what would it be and why?',
    is_deleted: false
  },
  {
    _id: '16',
    question: 'What is your favorite compliment to receive? Why?',
    firstPerson: 'What is my favorite compliment to receive? Why?',
    is_deleted: false
  }
];

export const getQuestionById = (id: string): Question | undefined => {
  return defaultQuestions.find(q => q._id === id);
};

export const getQuestionText = (question: any, isEditing: boolean): string => {
  const defaultQuestion = defaultQuestions.find(q => q.question === question);
  return defaultQuestion ? (isEditing ? defaultQuestion.question : defaultQuestion.firstPerson) : question;
};

export const getAllQuestions = (isEditing: boolean): string[] => {
  return defaultQuestions.map(q => isEditing ? q.question : q.firstPerson);
}; 
