@import 'bootstrap/scss/bootstrap';
@import 'bootstrap/scss/mixins';
@import '../../../Assets/Styles/typography';
@import '../../../Assets/Styles/colors';
@import '../../../Assets/Styles/mixins';

.AudioRecordingPopup {
  .modal-dialog {
    .modal-content {
      .modal-header {
        border-bottom: 0;
        padding-bottom: 0;

        button {
          position: absolute;
          right: 15px;
          top: 20px;
        }

        div {
          text-align: center;
          width: 100%;

          img {
            display: block;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin: auto;
          }

          p {
            display: block;
            max-width: 320px;
            margin: auto;
            margin-top: 8px;
            font: {
              size: 16px;
              family: $font-family-base;
              weight: $font-weight-medium;
            }
          }
        }
      }

      .Audio {
        .AudioPost {
          .UploadSec {
            border-radius: 10px;
            border: 1px dashed $grey-1;
            padding: 28px;
            text-align: center;
            min-height: 176px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;

            .row {
              align-items: center;
              width: 100%;

              .AudioWaves {
                flex: 0 1 calc(100% - 60px);
              }
            }

            .delete i {
              font-size: 24px;
              color: $title;
              cursor: pointer;
            }

            .ControllerIcon {
              width: 58px;
              height: 58px;
              flex: 0 1 58px;
              padding: 0;
              cursor: pointer;

              i {
                font-size: 58px;
                color: $success;
              }
            }

            .RecSec {
              cursor: pointer;
              width: 100%;

              span {
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;

                i {
                  padding-right: 5px;

                  &.fa-stoprecording {
                    color: #fc394d;
                  }
                }

                &:hover {
                  color: #fc394d;
                }
              }

              button {
                @include button-success;

                height: 40px;

                i {
                  padding-right: 5px;
                  font-size: 20px;
                }
              }
            }

            .Ripple {
              position: relative;
              margin: 0 auto 40px;
              width: 250px;
              height: 100px;
            }

            .dot::before {
              content: ' ';
              position: absolute;
              z-index: 2;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 20px;
              height: 20px;
              background-color: #355C7D;
              border-radius: 50%;
            }

            .dot::after {
              content: ' ';
              position: absolute;
              z-index: 1;
              width: 20px;
              height: 20px;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              background-color: #355C7D;
              border-radius: 50%;
              box-shadow: 0 0 20px rgb(0 0 0 / 30%) inset;
              animation-name: 'ripple';
              animation-duration: 1s;
              animation-timing-function: ease;
              animation-delay: 0s;
              animation-iteration-count: infinite;
              animation-direction: normal;
            }

            @keyframes ripple {
              0% {
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                opcity: 75;
                width: 0;
                height: 0;
              }

              100% {
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                opacity: 0;
                width: 100px;
                height: 100px;
              }
            }
          }
        }
      }

      .modal-footer {
        border-top: none;

        button {
          @include button-success;

          height: 40px;
        }
      }
    }
  }
}
