//@ts-nocheck
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../Redux/store';

// Create memoized selectors to prevent unnecessary rerenders
export const useUserState = () => {
  return useSelector((state: RootState) => state.user);
};

export const useUserData = () => {
  return useSelector((state: RootState) => state.user.value?.data);
};

export const usePageDetailState = () => {
  return useSelector((state: RootState) => state.pagedetail);
};

export const usePageDetailData = () => {
  return useSelector((state: RootState) => state.pagedetail.data);
};

export const useNotificationState = () => {
  return useSelector((state: RootState) => state.notification);
};

export const usePostState = () => {
  return useSelector((state: RootState) => state.post);
};

export const useMemoizedSelector = <T>(selector: (state: RootState) => T, deps: any[] = []) => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useMemo(() => useSelector(selector), deps);
}; 