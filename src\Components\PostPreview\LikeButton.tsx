// @ts-nocheck
//@ts-nocheck
import cn from 'classnames'
import { useDispatch } from 'react-redux'
import { trackEvent } from '../../Analytics/GA'
import { useAppSelector } from '../../Hooks'
import { postAddLikeThunk, removePostLikeThunk } from '../../Redux/thunks/postThunk'

const LikeButton = ({
  activePost,
  mediaObject,
  likeCount,
  setLikeCount,
  likes,
  setLikes,
  likeStatus,
  setLikeStatus,
  canAction,
}) => {
  const dispatch = useDispatch()
  const userState = useAppSelector(state => state.user)

  const checkLikedOrNot = (userId: string, postLikeObject: any) => {
    const likeUser = postLikeObject?.filter((e: any) => e?.createdBy?._id === userId)
    return likeUser?.length > 0
  }

  const addlikePost = (userId: string, postId: string, pageId: string) => {
    console.log('ADDING LIKE FOR USER', userId)
    let payload = {}
    if (activePost.activeIndex !== null) {
      payload = {
        media: mediaObject?._id,
        createdBy: userId,
      }
    } else {
      payload = {
        post: postId,
        createdBy: userId,
      }
    }

    const LikeAdd = {
      post: postId,
      createdBy: {
        _id: userId,
      },
      is_deleted: false,
    }

    setLikes([...likes, LikeAdd])
    setLikeCount(likeCount + 1)
    dispatch(postAddLikeThunk(payload, pageId, postId))
    trackEvent('Page Interaction', 'Post Like', `${userState?.value?.data?._id}`)
  }

  const removelikePost = (userId: string, postId: string, pageId: string, postLikeObject: any) => {
    console.log('REMOVING LIKE FOR USER', userId)
    const likeUser = postLikeObject?.find((e: any) => e?.createdBy?._id === userId)
    const likeId = likeUser?._id
    setLikeCount(likeCount - 1)
    const type = activePost.activeIndex !== null ? 'media' : 'post'
    const postIdInfo = activePost.activeIndex !== null ? mediaObject?._id : postId
    dispatch(removePostLikeThunk(postIdInfo, likeId, pageId, type, postId))
    trackEvent('Page Interaction', 'Remove Post Like', `${userState?.value?.data?._id}`)
  }

  return (
    <div className="LikeButton">
      {checkLikedOrNot(userState?.value?.data?._id, likes) ? (
        <i
          onClick={() => {
            removelikePost(userState?.value?.data?._id, activePost?._id, activePost?.page, likes)
          }}
          className={cn('fa fa-butterflylike', { disabled: !canAction() || !likeStatus })}
        ></i>
      ) : (
        <i
          onClick={() => {
            addlikePost(userState?.value?.data?._id, activePost?._id, activePost?.page)
          }}
          className={cn('fa fa-heartoutline', { disabled: !canAction() || !likeStatus })}
        ></i>
      )}
      {likeCount || (likes?.length ? likes?.length : '')}
    </div>
  )
}

export default LikeButton
