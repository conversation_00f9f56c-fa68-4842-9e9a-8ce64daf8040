// @ts-nocheck
import { Formik } from 'formik'
import { useEffect, useState } from 'react'
import Form from 'react-bootstrap/Form'
import DatePicker from 'react-datepicker'
import { useDispatch } from 'react-redux'
import * as Yup from 'yup'

import { environment } from '../../AppConfig/environment'
import { useAppSelector } from '../../Hooks'
import { changePaymentPlanResponse } from '../../Redux/slices/settingsSlice'
import { loading } from '../../Redux/slices/userSlice'
import { getPaymentDetails, paymentPlanChange } from '../../Redux/thunks/settingsThunk'
import Loader from '../Loader/Loader'
import AlertPopup from '../Modals/AlertPopup/AlertPopup'

const PaymentSettings = () => {
  const dispatch = useDispatch()
  const userState: any = useAppSelector(state => state.user)
  const settingsState = useAppSelector((state: any) => state?.settings)

  const [startDate, setStartDate] = useState<any>(new Date().toString())
  const [promoCode, setPromoCode] = useState<string>('')
  const [promoCodeErr, setPromoCodeErr] = useState<string>('')
  const [getCurrentPlanDetails, setCurrentPlanDetails] = useState<any>(undefined)

  const paymentSchema = Yup.object().shape({
    cardNumber: Yup.string().required('Card number is required').min(19, '').max(19, ''),
    expiration: Yup.string().required('Expiration is required'),
    cvv: Yup.string().required('CVV is required'),
  })

  const cardPaymentSubmit = (data: any) => {
    data.expiryYear = new Date(data?.expiration)?.getFullYear()
    data.expiryMonth = new Date(data?.expiration)?.getMonth()
    delete data.expiration
    const payload = { ...data, paymentType: 'stripe', userId: userState?.value?.data?._id }
    dispatch(loading(true))
    dispatch(paymentPlanChange(payload))
  }

  // For promocode payment & validation.
  const promoCodePayment = () => {
    setPromoCodeErr('')
    if (promoCode !== '' && promoCode !== environment.promocode) {
      setPromoCodeErr('Promo code not matched')
      return false
    } else if (promoCode !== '' && promoCode === environment.promocode) {
      // Call API
      const payload = { promoCode: environment.promocode, userId: userState?.value?.data?._id }
      dispatch(loading(true))
      dispatch(paymentPlanChange(payload))
    } else if (promoCode === '') {
      setPromoCodeErr('Promo code is required')
    }
  }

  const _getCurrentPlanDetail = () => {
    dispatch(loading(false))
    dispatch(getPaymentDetails(userState?.value?.data?._id))
  }

  useEffect(() => {
    if (settingsState?.changePaymentPlanResp?.success === true) {
      _getCurrentPlanDetail()
      const resetbtn = document.getElementById('PaymentFrm')
      if (resetbtn) {
        resetbtn?.click()
      }
      setPromoCode('')
    }
    if (settingsState?.changePaymentPlanResp?.success !== null) {
      dispatch(loading(false))
    }
  }, [settingsState?.changePaymentPlanResp?.success])

  useEffect(() => {
    _getCurrentPlanDetail()
  }, [])

  useEffect(() => {
    if (settingsState?.getPlanDetailResp?.success === true) {
      setCurrentPlanDetails(settingsState?.getPlanDetailResp?.data)
      dispatch(changePaymentPlanResponse({ ...settingsState?.getPlanDetailResp, success: null }))
    }
    if (settingsState?.getPlanDetailResp?.success !== null) {
      dispatch(loading(false))
    }
  }, [settingsState?.getPlanDetailResp?.success])

  return (
    <div className="paymentCard">
      {userState.loading && <Loader />}
      {getCurrentPlanDetails.length === 0 ||
      (getCurrentPlanDetails?.length && getCurrentPlanDetails[0].plan !== 'Premium') ? (
        <>
          <h4>Upgrade to Pro</h4>
          <p className="subHeading">
            With Pro account you can celebrate life events by creating multiple pages at just{' '}
            <span>${environment.proPlanAmount}</span> billed annually.
          </p>
          <Formik
            validateOnMount={true}
            validateOnChange={true}
            validationSchema={paymentSchema}
            onSubmit={cardPaymentSubmit}
            initialValues={{
              cardNumber: '',
              cvv: '',
              expiration: '',
            }}
          >
            {({ handleSubmit, handleChange, handleBlur, values, touched, isValid, errors, setValues, resetForm }) => (
              <Form noValidate onSubmit={handleSubmit} className="cardPayment">
                <span
                  className="d-none"
                  id="PaymentFrm"
                  onClick={() => {
                    resetForm()
                    document.getElementById('card-payment-btn')?.click()
                  }}
                ></span>
                <div className="row">
                  <div className="col-md-12 form-group position-relative">
                    <label htmlFor="card_number" className="form-label">
                      Card Number
                    </label>
                    <input
                      id="card_number"
                      type="text"
                      maxLength={19}
                      className="form-control"
                      placeholder="5555 4444 4444 4444"
                      name="cardNumber"
                      value={values.cardNumber}
                      onChange={e => {
                        const inputVal = e?.target?.value?.toString().replace(/ /g, '')
                        let inputNumbersOnly = inputVal?.replace(/\D/g, '')
                        if (inputNumbersOnly?.length > 16) {
                          inputNumbersOnly = inputNumbersOnly?.substr(0, 16)
                        }
                        const splits = inputNumbersOnly?.match(/.{1,4}/g)
                        let spacedNumber = ''
                        if (splits) {
                          spacedNumber = splits?.join(' ')
                        }
                        setValues({ ...values, cardNumber: spacedNumber })
                      }}
                    />
                    <svg
                      id="ccicon"
                      className="ccicon"
                      width="750"
                      height="471"
                      viewBox="0 0 750 471"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                    ></svg>
                  </div>
                  <div className="col-md-6 form-group m-50">
                    <label htmlFor="expiration" className="form-label">
                      Expiration
                    </label>
                    <DatePicker
                      dateFormat="MM/yy"
                      selected={startDate}
                      onChange={(date: any) => {
                        if (date !== null) {
                          setStartDate(date)
                          setValues({ ...values, expiration: date })
                        } else {
                          setStartDate('')
                          setValues({ ...values, expiration: '' })
                        }
                      }}
                      placeholderText={'MM/YY'}
                      className="form-control"
                      minDate={new Date()}
                      showMonthYearPicker
                    />
                  </div>
                  <div className="col-md-6 form-group m-50">
                    <label htmlFor="cvv" className="form-label">
                      CVV
                    </label>
                    <input
                      maxLength={4}
                      min={3}
                      id="cvv"
                      type="password"
                      className="form-control"
                      onChange={handleChange}
                      value={values.cvv}
                    />
                    <input type="hidden" name="expiration" value={values.expiration} onChange={handleChange} />
                  </div>
                  <div className="col-md-12 stripeLogo">
                    <img src="https://butterflyv2-production-assets.nyc3.digitaloceanspaces.com/image/7dfbf3d0-824c-4e77-b85f-0c028ef0749f-stripe-secure.png" />
                  </div>
                </div>
                <div className="paymentCta">
                  <button
                    className="btn btn-primary button card-payment-btn w-100"
                    id="card-payment-btn"
                    type="submit"
                    disabled={!isValid}
                  >
                    Complete Payment
                  </button>
                </div>
              </Form>
            )}
          </Formik>
          <form>
            <p className="discliamer">
              You agree to our Membership Terms of Service. Your payment method will be charged $20 USD a year. Delete
              your account whenever you like. No refunds for memberships canceled after 14 days of purchase.
            </p>
            <div className="separator">
              <div className="line"></div>
            </div>
            <div className="col-md-12 form-group mb-1">
              <h5>Use a Promo Code</h5>
              <div className="d-flex">
                <div className="form-group promoinput">
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Promo Code"
                    value={promoCode}
                    onChange={(e: any) => {
                      setPromoCode(e.target.value?.toString()?.trim())
                    }}
                  />
                </div>
                <div className="form-group promobtn">
                  <button
                    className="card-payment-btn"
                    id="card-payment-btn"
                    type="button"
                    disabled={promoCode === ''}
                    onClick={promoCodePayment}
                  >
                    Apply
                  </button>
                </div>
                <p className="text-danger">{promoCodeErr}</p>
              </div>
            </div>
          </form>
        </>
      ) : (
        <div>
          <h3>Plan has successfully upgraded to PRO</h3>
          <p>Your next payment due is on {getCurrentPlanDetails[0].billingDate}</p>
          <h3>Billing details</h3>
          {getCurrentPlanDetails[0].type === 'promoCode' ? (
            <p>{getCurrentPlanDetails[0].description[0].description}</p>
          ) : (
            <div>
              <p>
                <span>Name on card: </span>
                <span>{userState?.value?.data?.name}</span>
              </p>
              <p>
                <span>Billed to: </span>
                <span>**** 1234</span>
              </p>
              <p>
                <span>Exp Data: </span>
                <span>09/24</span>
              </p>
              <p>
                <span>Billing date: </span>
                <span>{getCurrentPlanDetails[0].billingDate}</span>
              </p>
            </div>
          )}
        </div>
      )}
      <AlertPopup
        buttonText={false}
        show={settingsState?.changePaymentPlanResp?.success !== null}
        content={settingsState?.changePaymentPlanResp?.message}
        state={
          settingsState?.changePaymentPlanResp?.success === true ||
          settingsState?.changePaymentPlanResp?.success === null
            ? 'SUCCESS'
            : 'ERROR'
        }
        onHide={() => {
          dispatch(
            changePaymentPlanResponse({
              ...settingsState?.changePaymentPlanResp,
              success: null,
            })
          )
        }}
      />
    </div>
  )
}
export default PaymentSettings
