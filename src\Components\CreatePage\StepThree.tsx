// @ts-nocheck
import { useState } from 'react'
import { Card } from 'react-bootstrap'
import Button from 'react-bootstrap/Button'
import Form from 'react-bootstrap/Form'
import { useNavigate } from 'react-router-dom'

import { butterflyBlack, logoNoText } from '../../commonservice'
import Loader from '../Loader/Loader'
import ProgBar from './ProgressBar'

function StepThree(props: any) {
  const navigate = useNavigate()
  const [approval, setApproval] = useState(1) // 1 means approval is on
  const d = new Date()
  const year = d.getFullYear()

  const handleToggle = () => {
    const newApproval = approval === 0 ? 1 : 0 // Toggle between 0 and 1
    setApproval(newApproval)
    props.handleForm({
      approval_required: newApproval === 1, // Send true or false
    })
  }

  return (
    <>
      <div className="imgHolder d-none d-md-block">
        <div className="contentWrapper stepThreeBg">
          <div className="sideBar">
            <div
              className="logo"
              onClick={() => {
                navigate('/mypages')
              }}
            >
              <img src={logoNoText} alt="Butterfly Logo" />
            </div>
            {window.location.pathname !== '/create' ? (
              <ul className="Back">
                <li
                  onClick={() => {
                    window.history.back()
                  }}
                >
                  <i className="fa fa-arrow-left"></i>
                </li>
              </ul>
            ) : null}
          </div>
          <div className="overlay"></div>
          <div className="title">
            Your Page. <br />
            Your Choice.
          </div>
        </div>
      </div>
      <div className="formHolder">
        {props.propsData[4] && <Loader />}
        <div className="d-xs-block d-md-none m-logo">
          <img src={butterflyBlack} alt="Butterfly logo" />
          <div className="title">Preserve Your Story. Inspire Future Generations.</div>
        </div>
        <ProgBar step={props.propsData[2]} />
        <div className="stepWrapper">
          <div className="steps stepTwo stepThree">
            <h2>Confirm these approval settings for your page.</h2>
            <p>If they’re OK, don’t do anything. They can be updated at any time.</p>
            <Form>
              <div className="privacy">
                <Card>
                  <Card.Body className="d-flex p-0">
                    <div className="lhs">
                      <div className="title">
                        <i className="fa fa-butterflyapproval" aria-hidden="true"></i>
                        <span>Approvals required</span>
                      </div>
                      <p className="subTitle">
                        When turned on, member posts must be approved by you before they appear on this Page.
                      </p>
                    </div>
                    <div className="rhs">
                      <label className="switch switch200">
                        <input type="checkbox" checked={approval === 1} onChange={handleToggle} />
                        <span className="slider slider200"></span>
                      </label>
                    </div>
                  </Card.Body>
                </Card>
              </div>

              <div className="justify-content-between d-flex">
                <Button className="prev" onClick={props.propsData[1]} disabled={props.propsData[2] === 1}>
                  Previous
                </Button>
                <Button
                  onClick={() => {
                    props.propsData[0]()
                    props.submitForm()
                  }}
                >
                  Add content to your page
                </Button>
              </div>
            </Form>
          </div>
        </div>
        <div className="position-absolute">
          <p className="CopyRight text-center position-fixed">© {year} Living Legacy</p>
        </div>
      </div>
    </>
  )
}

export default StepThree
