import React from 'react';
import { deepCompareEquals } from './useMemoCompare';

/**
 * Higher-order component that wraps a component with React.memo
 * and uses a deep comparison function for props
 * @param Component - The component to memoize
 * @returns Memoized component
 */
export function withDeepMemo<T>(Component: React.ComponentType<T>): React.MemoExoticComponent<React.ComponentType<T>> {
  return React.memo(Component, (prevProps: T, nextProps: T) => {
    return deepCompareEquals(prevProps, nextProps);
  });
}

/**
 * Higher-order component that wraps a component with React.memo and
 * a custom propsAreEqual function
 * @param Component - The component to memoize
 * @param propsAreEqual - Custom function to compare props
 * @returns Memoized component
 */
export function withCustomMemo<T>(
  Component: React.ComponentType<T>,
  propsAreEqual: (prevProps: Readonly<T>, nextProps: Readonly<T>) => boolean
): React.MemoExoticComponent<React.ComponentType<T>> {
  return React.memo(Component, propsAreEqual);
}
