@import 'bootstrap/scss/bootstrap';
@import 'bootstrap/scss/mixins';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/mixins';

// .panel-wrap .panel
.SearchPages {
  padding: 0;
  position: relative;
  background: #101416; // Dark background from dashboardbg
  min-height: 100vh;
  color: $white;
  padding-bottom: 40px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 280px;
    background: linear-gradient(135deg, rgba($primary-button, 0.4) 0%, rgba($primary-button, 0.1) 100%);
    z-index: 0;
    border-bottom: 1px solid rgba($success, 0.15);
  }

  .header {
    margin: 0 24px;
    position: relative;
    z-index: 1;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .fadeIn {
    animation: fadeIn 0.4s ease-in-out forwards;
  }
  
  .search-text-title {
    padding: 40px 32px 20px;
    color: $heading;
    font-size: 28px;
    font-weight: 600;
    line-height: 1.4;
    position: relative;
    z-index: 1;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 10px;
      left: 32px;
      width: 60px;
      height: 3px;
      background-color: $success;
      border-radius: 3px;
    }
  }
  
  .search-text {
    padding: 40px 32px 20px;
    color: $paragraph-2;
    line-height: 1.6;
    position: relative;
    z-index: 1;
    font-size: 16px;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 10px;
      left: 32px;
      width: 40px;
      height: 3px;
      background-color: rgba($success, 0.4);
      border-radius: 3px;
    }
  }
  
  .searchInput-container {
    padding: 0 32px 30px;
    position: relative;
    z-index: 1;
  }

  .searchInput {
    position: relative;
    color: $paragraph-2;
    font-size: 16px;
    display: inline-block;
    max-width: 100%;
    width: 100%;
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    border-radius: 12px;
    padding: 6px;
    background: $textarea-color;
    transition: all 0.3s ease;
    border: 1px solid rgba($success, 0.1);
    
    &:hover {
      box-shadow: 0 10px 25px rgba(0,0,0,0.25);
      border: 1px solid rgba($success, 0.2);
    }
    
    .search-icon {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 20px;
      color: $success;
      font-size: 20px;
      font-weight: bold;
      z-index: 2;
    }

    .search-label {
      color: $paragraph-2;
      font: {
        family: $font-family-base;
        weight: $font-weight-medium;
        size: 15px;
      }
      margin-bottom: 10px;
      display: block;
      padding: 0 10px;
    }

    .search-field {
      width: 100%;
      height: 56px;
      background: rgba($textarea-color, 0.7);
      border: 1px solid rgba($success, 0.2);
      border-radius: 10px;
      padding: 0 45px 0 50px;
      color: $white;
      font: {
        family: $font-family-base;
        weight: $font-weight-regular;
        size: 17px;
      }
      transition: all 0.3s ease;

      &:focus {
        outline: none;
        border: 1px solid $success;
        box-shadow: 0 0 0 4px rgba($success, 0.15);
        background: rgba($textarea-color, 0.9);
      }
      
      &::placeholder {
        color: $grey-1;
        opacity: 0.6;
      }
    }
    
    .clear-search {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba($success, 0.1);
      border: none;
      color: $paragraph-2;
      cursor: pointer;
      padding: 6px;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:hover {
        background: rgba($success, 0.2);
        color: $white;
      }
      
      i {
        font-size: 14px;
      }
    }
  }

  .searching-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 32px 20px;
    padding: 18px;
    background: rgba($success, 0.08);
    border-radius: 10px;
    border: 1px dashed rgba($success, 0.2);
    position: relative;
    z-index: 1;
    
    .dot-pulse {
      position: relative;
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: $success;
      color: $success;
      margin-right: 14px;
      
      &::before, &::after {
        content: '';
        display: inline-block;
        position: absolute;
        top: 0;
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background-color: $success;
        color: $success;
        opacity: 0.4;
      }
      
      &::before {
        left: -15px;
        animation: dot-pulse-before 1.5s infinite linear;
      }
      
      &::after {
        left: 15px;
        animation: dot-pulse-after 1.5s infinite linear;
      }
    }
    
    span {
      color: $success;
      font-weight: 500;
      font-size: 15px;
    }
  }
  
  @keyframes dot-pulse-before {
    0% { opacity: 0.4; }
    50% { opacity: 1; }
    100% { opacity: 0.4; }
  }
  
  @keyframes dot-pulse-after {
    0% { opacity: 0.4; }
    50% { opacity: 1; }
    100% { opacity: 0.4; }
  }
  
  .search-results-container {
    position: relative;
    z-index: 1;
    padding-top: 20px;
  }
  
  .search-count {
    padding: 0 32px 15px;
    color: $paragraph-2;
    font-size: 16px;
    font-weight: 500;
    
    &::after {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      background-color: $success;
      border-radius: 50%;
      margin-left: 8px;
      vertical-align: middle;
    }
  }

  .wrapper {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(4, 1fr);
    justify-content: flex-start;
    margin-bottom: 25px;
    overflow-y: overlay;
    padding: 30px 32px 50px;
    max-height: calc(100vh - 295px);
    position: relative;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(3, 1fr);
    }
    
    @include media-breakpoint-down(md) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include media-breakpoint-down(sm) {
      grid-template-columns: 1fr;
    }
    
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(to bottom, rgba($dashboardbg, 1) 0%, rgba($dashboardbg, 0) 100%);
      z-index: 2;
      pointer-events: none;
    }
    
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: rgba($textarea-color, 0.3);
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba($success, 0.3);
      border-radius: 4px;
      
      &:hover {
        background: rgba($success, 0.5);
      }
    }
  }

  .card {
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    min-width: calc(100% / 3);
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    background: $textarea-color;
    position: relative;
    border: 1px solid rgba($comment-line, 0.6);

    &:hover {
      box-shadow: 0 15px 35px rgba(0,0,0,0.25);
      cursor: pointer;
      transform: translateY(-5px);
      border: 1px solid rgba($success, 0.3);
    }

    .ImgHolder {
      width: 100%;
      height: auto;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      background: linear-gradient(45deg, darken($primary-button, 10%), $primary-button);
      background-repeat: no-repeat;
      background-position: center;
      position: relative;
      overflow: hidden;
      display: flex;
      justify-content: space-between;

      .card-img-top {
        width: 100%;
        height: 180px;
        object-fit: cover;
        transition: transform 0.8s ease;
      }
      
      &:hover .card-img-top {
        transform: scale(1.05);
      }
      
      .placeholder-image {
        width: 100%;
        height: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, rgba($textarea-color, 0.8) 0%, rgba($textarea-color, 1) 100%);
        transition: all 0.3s ease;
        
        i {
          font-size: 64px;
          color: rgba($comment-line, 0.6);
          transition: all 0.3s ease;
        }
        
        &:hover {
          background: linear-gradient(135deg, rgba($textarea-color, 0.9) 0%, rgba($textarea-color, 1) 100%);
          
          i {
            color: rgba($comment-line, 0.8);
            transform: scale(1.1);
          }
        }
      }

      .ImgWrapper {
        width: 100%;
        position: relative;
        height: 180px;

        .overlay {
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 100%;
          background: rgb(10, 15, 20);
          background: linear-gradient(180deg, rgba(10,15,20,0.01) 20%, rgba(0,0,0,0.85) 100%);
          opacity: 0.9;
          transition: opacity 0.3s ease;
        }
        
        &:hover .overlay {
          opacity: 1;
        }

        &:has(.card-img-top) {
          background: $textarea-color;
          height: unset;
        }

        .BottomSection {
          position: absolute;
          bottom: 0;
          padding: 18px;
          max-width: 100%;
          width: 100%;

          .page-name {
            margin: 0;
            line-height: 1.2;
            font: {
              size: 20px;
              family: $font-family-base;
              weight: $font-weight-semibold;
            }
            color: $white;
            text-shadow: 0 1px 3px rgba(0,0,0,0.8);
            transition: transform 0.3s ease;
            
            &:hover {
              transform: translateX(3px);
            }
          }

          .BottomIcons {
            color: $white;
            display: inline-flex;
            align-items: center;
            margin-top: 10px;
            width: 100%;

            .privacy {
              width: 100px;
              padding: 3px 10px;
              border-radius: 20px;
              display: flex;
              align-items: center;
              backdrop-filter: blur(8px);
              
              &.public {
                background-color: rgba($success, 0.3);
                border: 1px solid rgba($success, 0.2);
              }
              
              &.private {
                background-color: rgba(#ff9800, 0.3);
                border: 1px solid rgba(#ff9800, 0.2);
              }
              
              &.hidden {
                background-color: rgba(#f44336, 0.3);
                border: 1px solid rgba(#f44336, 0.2);
              }
            }

            .members {
              width: 140px;
              margin-left: 5px;
            }

            .subscribe {
              width: 70px;
              text-align: right;

              @include media-breakpoint-down(md) {
                width: 54px;
              }
            }

            span {
              display: inline-flex;
              align-items: center;
              padding-right: 15px;
              font: {
                size: 14px;
                family: $font-family-base;
                weight: $font-weight-regular;
              }

              &:last-child {
                padding-right: 0;
              }
            }

            i {
              color: $white;
              position: relative;
              font-size: 24px;
              padding-right: 3px;

              &.fa-membersicon {
                font-size: 18px;
              }
            }
          }
        }
      }
    }

    &__img {
      width: 100%;
      height: auto;
      object-fit: cover;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }

    &__body {
      flex: 1 0 auto;
      display: flex;
      flex-direction: column;
      margin: 1.5rem;

      .reason-text {
        flex-grow: 1;
        margin-bottom: 20px;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 0;
          width: 40px;
          height: 2px;
          background-color: rgba($success, 0.3);
        }
      }

      .owner {
        @include ProfilePic;
        color: $paragraph-2;
        min-height: 52px;
        border-top: 1px solid rgba($comment-line, 0.5);
        padding-top: 15px;
        margin-top: 15px;

        .ownerName {
          color: $paragraph-2;
          
          .owner-highlight {
            font-weight: 600;
            color: lighten($paragraph-2, 15%);
            position: relative;
            
            &::after {
              content: '';
              position: absolute;
              bottom: -2px;
              left: 0;
              width: 100%;
              height: 1px;
              background-color: rgba($success, 0.3);
            }
          }
        }
        
        .ownerPic {
          img {
            border: 2px solid $textarea-color;
            box-shadow: 0 3px 8px rgba(0,0,0,0.25);
          }
          
          .ownerAlpha {
            border: 2px solid $textarea-color;
            box-shadow: 0 3px 8px rgba(0,0,0,0.25);
            background-color: rgba($success, 0.2);
            color: $white;
          }
        }
      }

      p {
        font: {
          family: $font-family-base;
          weight: $font-weight-regular;
          size: 15px;
        }
        color: $paragraph-2;
        min-height: 60px;
        word-wrap: break-word;
        max-height: 90px;
        overflow: hidden;
        line-height: 1.6;

        @include media-breakpoint-down(md) {
          min-height: 0;
        }
      }
    }

    &__footer {
      margin-top: auto;
      margin: 15px 20px 20px;
      display: flex;
      justify-content: space-between;
      padding: 20px 0 0;
      border-top: 1px solid rgba($comment-line, 0.5);
      align-items: center;
    }

    &__btn {
      @include button-success;
      height: 42px;
      font-size: 15px;
      border-radius: 22px;
      padding: 0 24px;
      box-shadow: 0 4px 12px rgba($success, 0.15);
      transition: all 0.3s ease;
      font-weight: 500;
      background-color: $success;
      
      &:hover {
        box-shadow: 0 6px 14px rgba($success, 0.25);
        transform: translateY(-2px);
        background-color: $hover-button;
      }
      
      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba($success, 0.15);
      }
    }

    .subscription-area {
      display: flex;
      align-items: center;
    }

    .subscribe-button {
      color: $paragraph-2;
      display: inline-flex;
      align-items: center;
      font: {
        family: $font-family-base;
        weight: $font-weight-medium;
        size: 13px;
      }
      padding: 8px 14px;
      border-radius: 20px;
      transition: all 0.25s ease;

      span {
        padding-top: 0;
        line-height: 1;
      }

      i {
        font-size: 18px;
        padding-right: 5px;
      }
      
      &.owner-badge {
        background-color: rgba($success, 0.1);
        border: 1px solid rgba($success, 0.15);
        
        i, span {
          color: $success;
        }
      }
      
      &.joined {
        background-color: rgba($success, 0.1);
        border: 1px solid rgba($success, 0.15);
        cursor: pointer;
        
        &:hover {
          background-color: rgba($success, 0.15);
          box-shadow: 0 2px 8px rgba($success, 0.15);
          transform: translateY(-1px);
        }
        
        i, span {
          color: $success;
        }
      }
      
      &.pending {
        background-color: rgba(#ff9800, 0.1);
        border: 1px solid rgba(#ff9800, 0.15);
        
        i, span {
          color: #ff9800;
        }
      }
      
      &.join {
        background-color: rgba($success, 0.1);
        border: 1px solid rgba($success, 0.15);
        cursor: pointer;
        
        &:hover {
          background-color: rgba($success, 0.15);
          box-shadow: 0 2px 8px rgba($success, 0.15);
          transform: translateY(-1px);
        }
        
        i, span {
          color: $success;
        }
      }
    }
  }

  @include media-breakpoint-between(md, lg) {
    .wrapper {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @include media-breakpoint-up(lg) {
    .wrapper {
      grid-template-columns: repeat(4, 1fr);
      gap: 30px;
    }

    .card {
      flex-direction: column;
      height: 100%;
    }
  }

  .no-result {
    padding: 80px 0;
    width: 100%;
    text-align: center;
    color: $paragraph-2;
    
    .no-result-icon {
      margin-bottom: 20px;
      
      i {
        font-size: 64px;
        color: rgba($comment-line, 0.6);
        opacity: 0.8;
        text-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
    }
    
    .no-result-text {
      font-size: 24px;
      font-weight: 500;
      color: $paragraph-2;
      margin-bottom: 10px;
    }
    
    .no-result-subtext {
      font-size: 16px;
      color: rgba($paragraph-2, 0.7);
    }
  }

  @include media-breakpoint-down(md) {
    &::before {
      height: 200px;
    }
    
    .search-text-title,
    .search-text {
      padding: 25px 20px 15px;
      font-size: 20px;
      
      &::after {
        left: 20px;
      }
    }
    
    .searchInput-container {
      padding: 0 20px 20px;
    }
    
    .searching-indicator,
    .search-count {
      margin: 0 20px 15px;
    }
    
    .wrapper {
      padding: 20px 20px 30px;
    }
    
    .card {
      &__body {
        margin: 1.25rem;
        
        p {
          min-height: 0;
        }
      }
    }
  }
}
