
@import 'bootstrap/scss/bootstrap';
@import 'bootstrap/scss/mixins';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/mixins';

#FeaturesNav {
  list-style: none;
  margin-top: 16px;
  padding: 0;

  li {
    padding: 10px 8px 10px 14px;
    background-color: $white;
    border-radius: 50px;
    cursor: pointer;

    &.DesktopQuestion {
      display: inline-flex;

      @include media-breakpoint-down(xl) {
        display: none;
      }
    }

    &.legacy-highlight {
      position: relative;
      z-index: 99999;
      pointer-events: none;
    }

    &.PageMemberOnboarding-highlight {
      position: relative;
      z-index: 99999;
      pointer-events: none;
    }

    &.MobileQuestion {
      display: none;

      @include media-breakpoint-down(xl) {
        display: flex;
      }
    }

    &.DesktopCategories {
      position: relative;
      display: inline-flex;

      @include media-breakpoint-down(md) {
        display: none;
      }

      .categories-dropdown {
        position: absolute;
        top: calc(100% + 8px);
        left: 50%;
        transform: translateX(-50%);
        width: 220px;
        background: $title;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
        z-index: 1000;
        padding: 8px;
        border: 1px solid rgba(255, 255, 255, 0.1);

        &::before {
          content: '';
          position: absolute;
          top: -6px;
          left: 50%;
          transform: translateX(-50%);
          width: 12px;
          height: 12px;
          background: $title;
          border-left: 1px solid rgba(255, 255, 255, 0.1);
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          transform-origin: center;
          rotate: 45deg;
        }

        .category-item {
          width: 100%;
          padding: 10px 12px;
          display: flex;
          align-items: center;
          gap: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          color: $white;
          border-radius: 8px;
          margin-bottom: 2px;

          &:last-child {
            margin-bottom: 0;
          }

          font: {
            size: 14px;
            family: $font-family-dada-grotesk;
            weight: $font-weight-medium;
          }

          i {
            font-size: 16px;
            width: 20px;
            text-align: center;
            color: $success;
            flex-shrink: 0;
          }

          span {
            flex: 1;
            width : 100%;
            padding : 1px;
          }

          &:hover {
            background: rgba($success, 0.2);
            color: $white;

            i {
              color: $success;
            }
          }
        }
      }

      &:hover {
        background-color: $success;
        color: $white;

        i {
          color: $white;
        }

        .categories-dropdown {
          .category-item {
            color: $white;

            i {
              color: $success;
            }

            &:hover {
              color: $white;

              i {
                color: $success;
              }
            }
          }
        }
      }
    }

    font: {
      size: 16px;
      family: $font-family-base;
      weight: $font-weight-medium;
    }

    &:hover {
      cursor: pointer;
      background-color: $success;
      color: $white;

      i {
        color: $white;
      }

      span {
        background-color: rgb(228 229 230 / 20%);
        color: $white;
      }
    }

    color: $title;
    display: inline-flex;
    width: 100%;
    align-items: center;
    margin-bottom: 10px;

    i {
      color: $success;
      font-size: 24px;
      flex: 0 1 35px;
      max-width: 35px;
    }

    label {
      flex: 0 1 calc(100% - 70px);
      max-width: calc(100% - 70px);
      cursor: pointer;
    }

    span {
      flex: 0 1 30px;
      max-width: 30px;
      width: 30px;
      height: 30px;
      background-color: $bg;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      font: {
        size: 16px;
        family: $font-family-base;
        weight: $font-weight-medium;
      }

      color: $title;
    }
  }
}
