@import 'bootstrap/scss/bootstrap';
@import 'bootstrap/scss/mixins';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/mixins';

$container-width: 500px;
$container-height: 35px;
$container-padding-left: 10px;
$drop-list-max-height: 300px;
$list-item-height: 45px;
$ease-timing: 0.2s;

.MyPages {
  padding: 66px 80px 0;
  width: 100%;
  scroll-behavior: smooth;

  @include media-breakpoint-down(md) {
    padding: 100px 15px 0;
    scroll-behavior: smooth;
  }

  @include media-breakpoint-only(md) {
    padding: 66px 40px 0;
  }

  .TopBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-left: 20px;
    padding-right: 20px;

    @include media-breakpoint-down(md) {
      flex-wrap: wrap;
      padding: 0;
      margin-bottom: 0;
    }

    h2 {
      margin-bottom: 0;
      font: {
        family: $font-family-base;
        weight: $font-weight-bold;
        size: 32px;
      }
      letter-spacing: 2px;
      display: inline-flex;
      align-items: center;
      color: $title;

      span {
        margin-left: 10px;
        min-width: 38px;
        width: max-content;
        padding: 0 10px;
        height: 38px;
        background-color: $white;
        border-radius: 50px;
        display: inline-flex;
        color: $success;
        justify-content: center;
        align-items: center;
        font: {
          family: $font-family-base;
          weight: $font-weight-medium;
          size: 16px;
        }
      }

      @include media-breakpoint-down(md) {
        padding-bottom: 22px;
        border-bottom: 1px solid silver;
        width: 100%;
        padding-left: 5px;
        font-size: 24px;
      }
    }

    &__right {
      font: {
        family: $font-family-base;
        weight: $font-weight-semibold;
        size: 16px;
      }

      display: inline-flex;
      align-items: center;
      margin-left: 5px;

      @include SelectDropdown;

      @include media-breakpoint-down(md) {
        .butterfly-dropdown-select {
          width: 180px;
        }
      }

      span {
        display: inline-flex;
        align-items: center;

        @include media-breakpoint-down(md) {
          padding-top: 20px;
          width: 100%;
        }
      }

      button.Cta {
        @include button-success;

        height: 40px;
        margin-left: 20px;

        i {
          margin-right: 8px;
          font-size: 16px;
        }

        margin-bottom: 0;

        @include media-breakpoint-down(lg) {
          display: none;
        }
      }
    }
  }

  .wrapper {
    display: grid;
    gap: 30px;
    grid-template-columns: 1fr;
    justify-content: flex-start;
    margin-bottom: 0;
    overflow-y: auto;
    max-height: calc(100vh - 290px);
    min-height: calc(100vh - 290px);
    scroll-behavior: smooth;
    padding: 20px;
    padding-bottom: 100px;

    @include media-breakpoint-down(md) {
      max-height: unset;
      padding: 20px 0;
      min-height: calc(100vh - 363px);
    }

    @include media-breakpoint-between(md, xl) {
      max-height: calc(100vh - 460px);
      min-height: calc(100vh - 460px);
      overflow-y: auto;
      padding: 20px 20px 100px;
      scroll-behavior: smooth;
    }

    @include media-breakpoint-only(xl) {
      grid-template-columns: repeat(auto-fit, minmax(260px, 283px));
    }

    &:empty {
      min-height: calc(100% - 220px);

      //height: auto;
      @include media-breakpoint-down(md) {
        min-height: 50vh;
      }
    }
  }

  .card {
    border-radius: 5px;

    //min-width: calc(100% / 3);
    display: flex;
    border: 1px solid $bg;
    height: max-content;

    @include media-breakpoint-up(xl) {
      &:hover {
        box-shadow: 0 0 34px -7px rgb(0 0 0 / 45%);
        cursor: pointer;
        transition: ease 0.2s;
      }
    }

    .ImgHolder {
      width: 100%;
      height: auto;
      border-radius: 5px;
      background: url('https://butterfly-staging-assets.nyc3.digitaloceanspaces.com/image/88ee1d60-c99a-45aa-bf60-963cac58a287-logoipsum-370.png'),
        #355C7D;
      background-repeat: no-repeat;
      background-position: center;
      position: relative;
      overflow: hidden;
      display: flex;
      justify-content: space-between;

      .card-img-top {
        width: 100%;
      }

      .ImgWrapper {
        width: 100%;
        position: relative;
        height: 215px;

        @include media-breakpoint-between(md, xl) {
          height: 165px;
        }

        .overlay {
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 100%;
          background: rgb(255 249 249);
          background: linear-gradient(180deg, rgb(255 249 249 / 1.03%) 41%, rgb(0 0 0 / 52.6%) 97%);
        }

        .BottomSection {
          position: absolute;
          bottom: 0;
          padding: 8px 10px;
          max-width: 100%;
          width: 100%;

          p {
            margin: 0;
            line-height: 1.3;
            font: {
              size: 20px;
              family: $font-family-base;
              weight: $font-weight-semibold;
            }
            letter-spacing: 2px;
            color: $white;
          }

          .BottomIcons {
            color: $white;
            display: inline-flex;
            align-items: center;
            margin-top: 4px;
            width: 100%;
            justify-content: flex-start;

            .privacy {
              width: 100px;
            }

            .members {
              width: 140px;

              @include media-breakpoint-between(md, xl) {
                width: 280px;
              }
            }

            .subscribe {
              width: 70px;
              text-align: right;
              display: inline-block;

              @include media-breakpoint-down(md) {
                width: 54px;
              }
            }

            span {
              display: inline-flex;
              align-items: center;
              padding-right: 15px;
              font: {
                size: 14px;
                family: $font-family-dada-grotesk;
                weight: $font-weight-regular;
              }

              &:last-child {
                padding-right: 0;
              }
            }

            i {
              color: $white;
              position: relative;
              font-size: 24px;
              padding-right: 3px;

              &.fa-membersicon {
                font-size: 18px;
              }
            }

            @include media-breakpoint-down(xl) {
              justify-content: space-between;
            }
          }
        }
      }
    }

    &__img {
      width: 100%;
      height: auto;
      object-fit: cover;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    }

    &__body {
      margin: 1rem;
      flex-grow: 0;

      .owner {
        @include ProfilePic;

        color: $text-primary;

        .ownerName {
          color: $text-primary;
        }
      }

      p {
        font: {
          family: $font-family-base;
          weight: $font-weight-regular;
          size: 15px;
        }

        color: $text-primary;
        min-height: 90px;
        word-wrap: break-word;
        max-height: 90px;
        overflow: hidden;

        @include media-breakpoint-down(md) {
          min-height: unset;
        }
      }
    }

    &__footer {
      margin: 10px;
      display: flex;
      justify-content: center;
      padding: 10px 0 0;
      border-top: 1px solid $bg;
      align-items: center;
    }

    &__btn {
      @include button-success;

      height: 40px;
      font-size: 14px;
    }

    .subscribe-button {
      color: #777;
      display: inline-flex;
      align-items: center;
      font: {
        family: $font-family-base;
        weight: $font-weight-medium;
        size: 12px;
      }

      span {
        padding-top: 0;
        line-height: 1;
      }

      i {
        font-size: 18px;
        padding-right: 5px;
        color: $success;
      }

      &.active {
        i {
          color: $success;
        }
      }

      .subscribed {
        color: $success;
      }

      .peding {
        color: #777;
      }
    }
  }

  @include media-breakpoint-between(md, lg) {
    .wrapper {
      grid-template-columns: repeat(auto-fit, minmax(240px, 270px));
    }
  }

  @include media-breakpoint-up(lg) {
    .wrapper {
      grid-template-columns: repeat(auto-fit, minmax(260px, 325px));
    }

    .card {
      flex-direction: column;

      //min-width: calc(100% / 3);

      &__img {
        width: 100%;
        height: auto;
      }
    }
  }

  .Footer {
    text-align: center;
    position: relative;

    //margin-bottom: 100px;
    margin-top: 10px;

    &::before {
      content: '';
      position: absolute;
      width: 100%;
      background: linear-gradient(180deg, hsl(0deg 0% 100% / 0%) 0%, $bg 97%);
      bottom: 35px;
      height: 50px;
      left: 0;
      top: -60px;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    button {
      @include button-success;

      height: 60px;

      i {
        margin-right: 8px;
        font-size: 20px;
      }

      margin-bottom: 35px;
    }

    p {
      font: {
        size: 12px;
        family: $font-family-base;
        weight: $font-weight-regular;
      }

      color: #777;

      a {
        cursor: pointer;
      }
    }

    .Emptydiv {
      display: none;
    }

    @include media-breakpoint-down(md) {
      .Emptydiv {
        display: block;
        height: 190px;
      }

      button {
        position: fixed;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        border: 2px solid $white;
        height: 50px;
        padding-left: 20px;
        padding-right: 20px;
        width: max-content;
        box-shadow: 0 1px 20px rgb(0 0 0 / 20%);
      }
    }
  }
}
