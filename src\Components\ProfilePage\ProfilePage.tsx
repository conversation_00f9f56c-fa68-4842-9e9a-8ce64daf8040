//@ts-nocheck
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../Hooks';
import { CenterSection } from '../../Layouts/LayoutSections/MidSection';
import CreatePostSec from '../PageDetails/createPost/index';
import { pageDetailThunk } from '../../Redux/thunks/pageDetailThunk';
import Loader from '../Loader/Loader';
import './ProfilePage.scss';

interface ProfilePageProps {
  // No props needed since we'll get pageUrl from URL params and Redux state
}

const profileQuestions = [
  { 
    id: 1, 
    original: "Who was the biggest influence in your life?",
    firstPerson: "Who was the biggest influence in my life?",
    defaultAnswer: "I was deeply influenced by..." 
  },
  { 
    id: 2, 
    original: "What major event or realization shaped who you are?",
    firstPerson: "What major event or realization shaped who I am?",
    defaultAnswer: "A pivotal moment in my life was..." 
  },
  { 
    id: 3, 
    original: "Who was your closest friend growing up?",
    first<PERSON>erson: "Who was my closest friend growing up?",
    defaultAnswer: "My closest childhood friend was..." 
  },
  { 
    id: 4, 
    original: "What was the most important lesson you learned as a parent?",
    firstPerson: "What was the most important lesson I learned as a parent?",
    defaultAnswer: "The most valuable parenting lesson I learned..." 
  },
  { 
    id: 5, 
    original: "What is the best advice you could give your child?",
    firstPerson: "What is the best advice I could give my child?",
    defaultAnswer: "The most important advice I would give is..." 
  },
  { 
    id: 6, 
    original: "What phrase has kept you afloat during hard times?",
    firstPerson: "What phrase has kept me afloat during hard times?",
    defaultAnswer: "The words that helped me through difficult times..." 
  },
  { 
    id: 7, 
    original: "What advice do you have about growing older?",
    firstPerson: "What advice do I have about growing older?",
    defaultAnswer: "My perspective on aging is..." 
  },
  { 
    id: 8, 
    original: "What are some of your favorite memories growing up?",
    firstPerson: "What are some of my favorite memories growing up?",
    defaultAnswer: "My cherished childhood memories include..." 
  },
  { 
    id: 9, 
    original: "When life was difficult, what did you do to overcome it?",
    firstPerson: "When life was difficult, what did I do to overcome it?",
    defaultAnswer: "I overcame life's challenges by..." 
  },
  { 
    id: 10, 
    original: "What is one thing you haven't done you regret?",
    firstPerson: "What is one thing I haven't done I regret?",
    defaultAnswer: "One regret I carry is..." 
  },
  { 
    id: 11, 
    original: "What are you most proud of having done in life?",
    firstPerson: "What am I most proud of having done in life?",
    defaultAnswer: "My proudest achievement is..." 
  },
  { 
    id: 12, 
    original: "Do you have any superstitions?",
    firstPerson: "Do I have any superstitions?",
    defaultAnswer: "When it comes to superstitions..." 
  },
  { 
    id: 13, 
    original: "What do you hold sacred?",
    firstPerson: "What do I hold sacred?",
    defaultAnswer: "The things I hold most sacred are..." 
  },
  { 
    id: 14, 
    original: "Did you have a nickname? What is the story behind it?",
    firstPerson: "Did I have a nickname? What is the story behind it?",
    defaultAnswer: "The story of my nickname..." 
  },
  { 
    id: 15, 
    original: "If you could redo any period in your life, what would it be and why?",
    firstPerson: "If I could redo any period in my life, what would it be and why?",
    defaultAnswer: "If I could go back in time..." 
  },
  { 
    id: 16, 
    original: "What is your favorite compliment to receive? Why?",
    firstPerson: "What is my favorite compliment to receive? Why?",
    defaultAnswer: "The compliment that means the most to me..." 
  }
];

const ProfilePage: React.FC<ProfilePageProps> = () => {
  const { pageUrl } = useParams();  // Changed from pageId
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userState = useAppSelector(state => state.user);
  const pageDetailState = useAppSelector(state => state.pagedetail.data);
  const isLoggedIn = !!userState?.value?.data?._id;
  const [answers, setAnswers] = useState(
    profileQuestions.reduce((acc, q) => ({ ...acc, [q.id]: q.defaultAnswer }), {})
  );
  const [isEditing, setIsEditing] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [isPostsMediaExpanded, setIsPostsMediaExpanded] = useState(true);
  const [activeSlide, setActiveSlide] = useState(0);
  const [isCarouselPaused, setIsCarouselPaused] = useState(false);

  useEffect(() => {
    if (pageUrl) {
      // Load page details if not already loaded or if it's a different page
      if (!pageDetailState || pageDetailState.unique_url !== pageUrl) {
        dispatch(pageDetailThunk(pageUrl, userState?.value?.data?._id));
      }
      setLoading(false);
    }
  }, [pageUrl, dispatch, pageDetailState, userState?.value?.data?._id]);

  // Auto-play carousel
  useEffect(() => {
    if (!isCarouselPaused && !isEditing) {
      const interval = setInterval(() => {
        setActiveSlide(prev => (prev + 1) % profileQuestions.length);
      }, 4000); // Change slide every 4 seconds

      return () => clearInterval(interval);
    }
  }, [isCarouselPaused, isEditing, profileQuestions.length]);

  const handleAnswerChange = (questionId: number, value: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  const handleGoBack = () => {
    navigate('/mypages');
  };

  const togglePostsMedia = () => {
    setIsPostsMediaExpanded(!isPostsMediaExpanded);
  };

  const goToSlide = (index: number) => {
    setActiveSlide(index);
  };

  const nextSlide = () => {
    setActiveSlide(prev => (prev + 1) % profileQuestions.length);
  };

  const prevSlide = () => {
    setActiveSlide(prev => (prev - 1 + profileQuestions.length) % profileQuestions.length);
  };

  const handleCarouselMouseEnter = () => {
    setIsCarouselPaused(true);
  };

  const handleCarouselMouseLeave = () => {
    setIsCarouselPaused(false);
  };

  if (loading || !pageDetailState) {
    return <Loader />;
  }

  return (
    <div className="profile-page">
      <div className="profile-header-bar">
        <button className="back-button" onClick={handleGoBack}>
          <i className="fa fa-arrow-left"></i>
          Back to My Pages
        </button>
        {isLoggedIn && (
          <button
            className="edit-profile-btn"
            onClick={() => setIsEditing(isEditing ? null : -1)}
          >
            <i className="fa fa-edit"></i>
            {isEditing ? 'Done' : 'Edit Profile'}
          </button>
        )}
      </div>
      <div className="profile-body">
        <div className="profile-content">
          <div className="profile-header">
            <div className="profile-image">
              {pageDetailState?.image ? (
                <img src={pageDetailState.image} alt="Profile" />
              ) : (
                <div className="profile-initial">
                  {pageDetailState?.first_name?.charAt(0) || 'U'}
                </div>
              )}
            </div>
            <div className="profile-info">
              <h2>{pageDetailState?.first_name + ' ' + pageDetailState?.last_name || 'User Name'}</h2>
              {isLoggedIn && (
                <p className="email">{userState?.value?.data?.email}</p>
              )}
            </div>
          </div>

          <div className="qa-section">
            <h3>Get to Know Me</h3>
            <div
              className="qa-carousel"
              onMouseEnter={handleCarouselMouseEnter}
              onMouseLeave={handleCarouselMouseLeave}
            >
              <button className="carousel-nav prev" onClick={prevSlide}>
                <span>‹</span>
              </button>

              <div className="carousel-container">
                <div
                  className="carousel-track"
                  style={{ transform: `translateX(-${activeSlide * 100}%)` }}
                >
                  {profileQuestions.map((q, index) => {
                    const isActive = index === activeSlide;
                    const isVisible = Math.abs(index - activeSlide) <= 1;

                    return (
                      <div
                        key={q.id}
                        className={`qa-card ${isActive ? 'active' : 'inactive'} ${isVisible ? 'visible' : 'hidden'}`}
                        onClick={() => !isActive && goToSlide(index)}
                      >
                        <h4>{isEditing === q.id ? q.original : q.firstPerson}</h4>
                        {isActive && (
                          <>
                            {isEditing === q.id ? (
                              <textarea
                                value={answers[q.id]}
                                onChange={(e) => handleAnswerChange(q.id, e.target.value)}
                                rows={3}
                                placeholder="Share your answer..."
                                onFocus={handleCarouselMouseEnter}
                                onBlur={handleCarouselMouseLeave}
                              />
                            ) : (
                              <p onClick={() => isLoggedIn && setIsEditing(q.id)}>
                                {answers[q.id]}
                                {isLoggedIn && (
                                  <i className="fa fa-edit edit-icon"></i>
                                )}
                              </p>
                            )}
                          </>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              <button className="carousel-nav next" onClick={nextSlide}>
                <span>›</span>
              </button>

              <div className="carousel-indicators">
                {profileQuestions.map((_, index) => (
                  <button
                    key={index}
                    className={`indicator ${index === activeSlide ? 'active' : ''}`}
                    onClick={() => goToSlide(index)}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Center Section Content */}
          <div className="center-section-wrapper">
            {/* This header is ALWAYS visible */}
            <div className="section-header" onClick={togglePostsMedia}>
              <h3>Posts & Media</h3>
              <span className="toggle-arrow" title="Click to toggle">
                {isPostsMediaExpanded ? '▲' : '▼'}
              </span>
            </div>
            
            {/* This content is conditionally rendered */}
            {isPostsMediaExpanded && (
              <div className="center-section-content">
                <CenterSection
                  component={{
                    midView: [<CreatePostSec key="create-post-sec" />]
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;





