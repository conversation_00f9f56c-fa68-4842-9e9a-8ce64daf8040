# Butterfly V2.0 App
<hr><p>Butterfly is for all of life's important moments.</p>
<p>This could be your birthday, remembering someone who has passed, a wedding, the birth of a child, or anything you want to remember in the most beautiful way possible!</p><h2>General Information</h2>
<hr><ul>
<li>Feed: Turn emotions into narratives, where every story, photo, and video paints a vivid picture of your life's journey.</li>
</ul><ul>
<li>Questions: Delve deeper into your experiences, guided by thoughtful prompts that help illuminate the richness of your life's tapestry.</li>
</ul><ul>
<li>Events: Never miss a moment worth celebrating or remembering, inviting your community to share in those pivotal times.</li>
</ul><h2>Technologies Used</h2>
<hr><ul>
<li>HTML</li>
</ul><ul>
<li>CSS</li>
</ul><ul>
<li>JavaScript</li>
</ul><ul>
<li>React</li>
</ul><ul>
<li>NodeJS</li>
</ul><ul>
<li>Redux-toolkit.js</li>
</ul><ul>
<li>React Axios</li>
</ul><h2>Features</h2>
<hr><ul>
<li>Feed</li>
</ul><ul>
<li>Questions</li>
</ul><ul>
<li>Events</li>
</ul><ul>
<li>Gallery</li>
</ul><ul>
<li>Fundraiser</li>
</ul><h2>Screenshots</h2>
<hr><p><img src="https://butterflyv2-production-assets.nyc3.digitaloceanspaces.com/image/dc23db1c-50b5-4fc7-ae5f-c68dd04ee7b7-01.jpg" alt=""></p><p><img src="https://butterflyv2-production-assets.nyc3.digitaloceanspaces.com/image/e6aac589-aec3-495a-aaa1-efe38c69df05-02.jpg" alt=""></p><p><img src="https://butterflyv2-production-assets.nyc3.digitaloceanspaces.com/image/6bd00b7d-eb78-4708-8771-43f86d1a1f9b-03.jpg" alt=""></p><p><img src="https://butterflyv2-production-assets.nyc3.digitaloceanspaces.com/image/6b1fe038-bd2d-4096-afe2-22417b85a514-04.jpg" alt=""></p><h2>Setup</h2>
<hr><p>The following are the requirements to run this project:</p><h5>Steps</h5><ul>
<li>Node Package Manager (NPM)</li>
</ul><ul>
<li>Node.js Version (18.x)</li>
</ul><ul>
<li>React.js</li>
</ul><h2>Project Run</h2>
<hr><p>Download the latest source code from the git repo:</p>
<p>Run <code>npm install</code></p>
<p>Run <code>npm start</code></p>

# Living Legacy Frontend

## Article Feature Documentation

### Overview
The article feature allows users to create and view long-form content with rich text formatting. Unlike regular posts, articles support full formatting options including headings, lists, links, and embedded media.

### Implementation Details

#### Creating Articles
1. In the CreatePost modal, users can toggle between "Post" and "Article" modes
2. When in Article mode, a rich text editor (QuillJS) is displayed instead of the plain text area
3. The modal expands to full-screen in Article mode to provide more space for writing
4. Media can be added to articles using the same media selection tools as regular posts

#### Article Preview in Feed
- Articles appear in the feed with a preview of the content
- The preview shows a limited amount of text with a gradient fade at the bottom
- A "Read Full Article" button allows users to navigate to the full article view

#### Full Article View
- The full article is displayed in a dedicated page at `/article/:postId`
- All rich text formatting is preserved in the full view
- Media is displayed in its original size and quality
- Navigation controls allow returning to the previous page

### Technical Implementation
- Rich text editing uses ReactQuill with custom toolbar configuration
- Article content is stored in the post's description field with isArticle flag set to true
- Media handling is consistent with the existing post media system

### Styling
The article feature includes custom styles for:
- Article previews in the feed
- Full article view with optimized typography for readability
- Media display in articles
- Navigation controls and UI elements


- NOTES _ 
Logged out user cannot see email id in about me
- user landing page 
    - profile description should be there in about me section
    - if private profile then only show name and image in about me
    - add a carousel for questions like in techneeqs.com
- Feed - 
- add a carousel for recent media like in techneeqds.com
- in recent posts and recent articles add gradient border and fix design

- After login show a dashboard screen for authors 
- show total posts
- Show total articles
- show total photos
- show total videos

the whole about me section will go inside my account