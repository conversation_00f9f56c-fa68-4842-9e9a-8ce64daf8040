@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';
@import '../../../Assets/Styles/typography';
@import '../../../Assets/Styles/colors';
@import '../../../Assets/Styles/mixins';

.recent-posts-carousel {
  margin: 20px 0;
  padding: 0;
  width: 100%;

  h4 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
    padding: 0 20px;
  }

  .carousel-wrapper {
    position: relative;
    padding: 0 40px;
    width: 100%;

    .control {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;

      &.prev {
        left: 10px;
      }

      &.next {
        right: 10px;
      }

      i {
        font-size: 14px;
        color: #666;
      }
    }

    > div {
      display: flex;
      justify-content: center;
      width: 100%;
      max-width: 100% !important;
    }
  }

  .post-item {
    margin: 0 15px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    height: 280px;
    width: calc(33.33% - 30px);
    max-width: 350px;
    min-width: 280px;
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-5px);
    }

    .post-media {
      height: 160px;
      background-size: cover;
      background-position: center;
      position: relative;

      .play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: white;
          font-size: 16px;
        }
      }
    }

    .post-content {
      padding: 15px;

      .post-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .post-description {
        font-size: 14px;
        color: #666;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .post-meta {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #888;

        .post-date {
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .post-stats {
          display: flex;
          align-items: center;
          gap: 10px;

          span {
            display: flex;
            align-items: center;
            gap: 3px;
          }
        }
      }
    }
  }
}

@media (max-width: 1024px) {
  .recent-posts-carousel {
    .post-item {
      width: calc(50% - 30px);
    }
  }
}

@media (max-width: 768px) {
  .recent-posts-carousel {
    padding: 0;

    .carousel-wrapper {
      padding: 0 30px;
    }

    .post-item {
      width: calc(100% - 30px);
      height: 250px;
      margin: 0 10px;

      .post-media {
        height: 140px;
      }

      .post-content {
        padding: 12px;

        .post-title {
          font-size: 14px;
        }

        .post-description {
          font-size: 12px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .recent-posts-carousel {
    .post-item {
      height: 230px;
      max-width: 240px;

      .post-media {
        height: 120px;
      }
    }
  }
} 