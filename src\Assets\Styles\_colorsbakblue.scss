:root {
  --color-primary: #2A3E52;        // Deep, muted blue (main brand color)
  --color-secondary: #F8B195;      // Warm Gold (luxury, premium)
  --color-additional: #6C7A89;     // Muted Olive Green (subtle accent)
}

$success : #F8B195
$white: #ffffff;                  // Pure white (for light text)
$text-primary: #E0E0E0;           // Light gray for text (easy on the eyes)
$title: #355C7D;                  // Gold for headings (prominent)
$bg: #fcfcfc;                     // Very dark gray background (near black)
// $bg: #12171C;                     // Very dark gray background (near black)
$grey-1: #bcc0c5;                 // Muted gray (for secondary elements)
// $success: #eeab91;                // Fresh green (used for success states)
$error: #fc394d;                  // Bright red for errors
$Nuetral-900: #D1D4D9;            // Light gray for neutral text
$primary-button: #355C7D;         // Deep blue for primary actions
$secondary-button: $success;      // Fresh green for secondary actions
$success-button: $success;        // Green for success button
$hover-button: darken(#F8B195, 12%); // Darker gold for hover effect
$cta-focus: #6C7A89;              // Muted green for focus on CTAs
$google-btn: $success;            // Green for Google button
$disabled-button: #aeb2b9;        // Dark muted gray for disabled buttons
$paragraph-2: #D1D4D9;            // Soft gray for paragraph text
$heading: #F8B195;                // Gold for headings (to stand out)
$lp-cta-bg: #355C7D;              // Deep blue background for CTAs
$dashboardbg: #101416;            // Very dark background for dashboard (luxury feel)
$error-color: #F8B195;            // Softer red for error notifications (with gold undertones)
$textarea-color: #2C2F36;         // Dark charcoal for textareas
$comment-line: #3A3D42;           // Subtle separator line color
$color-green: #eeab91; 
$button-success: #eeab91          // Consistent green for buttons and success <---
