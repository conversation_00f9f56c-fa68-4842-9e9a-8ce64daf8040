:root {
    --color-primary: #355C7D;       // Soft Deep Blue
    --color-secondary: #F8B195;     // Warm Gold
    --color-accent: #6C7A89;        // <PERSON> Green (small touches only)
  }
  
  $white: #ffffff;
  $bg: #FFF8F0;                     // Soft Ivory / Light Beige
  $text-primary: #333333;           // Charcoal Gray
  $text-secondary: #707070;         // Muted Slate
  
  // Typography
  $heading: #355C7D;                // Use Soft Deep Blue for headings
  $paragraph-2: #333333;            // Primary text (again, Charcoal Gray)
  $title: #F8B195;                  // Warm Gold (maybe for highlighted titles)
  
  // Buttons
  $primary-button: #355C7D;         // Soft Deep Blue
  $secondary-button: #F8B195;       // Warm Gold
  $success-button: #6C7A89;         // Olive Green for subtle CTAs
  $hover-button: darken(#F8B195, 10%);  // Darker gold on hover
  $disabled-button: #c6c8ca;        // Light neutral gray
  
  // Forms & UI
  $textarea-color: #f9f7ff;         // Slight off-white for form elements
  $comment-line: #dddddd;           // Subtle line
  $dashboardbg: #f5f5f5;            // Light gray for dashboard background
  
  // Notifications
  $success: #eeab91;                // Bright success green
  $error: #fc394d;                  // Bright error red
  $error-color: #f49f9f;            // Softer error tone
  
  // Call-To-Actions (optional)
  $cta-focus: #6C7A89;              // Olive Green (subtle CTA accent)
  $lp-cta-bg: #29cc97;              // Can be re-evaluated if it clashes
  
  // External Service Buttons
  $google-btn: #eeab91;             // Reuses success green for continuity
  
  // Neutrals (optional refinement)
  $grey-1: #c6c8ca;
  $Nuetral-900: #333333;            // Charcoal Gray as deep neutral
  