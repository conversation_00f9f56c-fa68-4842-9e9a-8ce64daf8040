import { useEffect, useRef } from 'react';

/**
 * Custom hook that works like useEffect, but deeply compares objects and arrays
 * to prevent unnecessary rerenders.
 * @param value - The value to memoize
 * @param compare - Function to compare the previous and next values
 * @returns The memoized value that only changes when the comparison returns false
 */
export default function useMemoCompare(next: any, compare: (prev: any, next: any) => boolean) {
  // Ref for storing previous value
  const previousRef = useRef();
  const previous = previousRef.current;
  
  // Pass previous and next value to compare function
  // to determine if they are equivalent
  const isEqual = compare(previous, next);
  
  // If not equal update previousRef to next value
  // We only update if not equal, so that this hook continues to return
  // the same value if compare keeps returning true
  useEffect(() => {
    if (!isEqual) {
      previousRef.current = next;
    }
  });
  
  // Return previous if compare function returns true,
  // otherwise return next
  return isEqual ? previous : next;
}

/**
 * Deep compare objects
 * @param obj1 - First object to compare
 * @param obj2 - Second object to compare
 * @returns boolean indicating if objects are equal
 */
export function deepCompareEquals(obj1: any, obj2: any) {
  if (obj1 === obj2) return true;
  
  if (typeof obj1 !== typeof obj2) return false;
  if (obj1 === null || obj2 === null) return obj1 === obj2;
  if (typeof obj1 !== 'object') return obj1 === obj2;
  
  // Both are objects or arrays at this point
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  return keys1.every(key => deepCompareEquals(obj1[key], obj2[key]));
} 