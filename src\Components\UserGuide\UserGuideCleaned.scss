@import 'bootstrap/scss/bootstrap';
@import 'bootstrap/scss/mixins';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/mixins';

.myTooltipClass {
  //transform: translate(0px, 100px);
  width: 300px;

  .introjs-skipbutton {
    display: none;
  }

  .introjs-arrow {
    //height: 100%;
    border: none;

    &::before {
      background-image: url('../../Assets/Images/arrow.svg');
      width: 100px;
      height: 100px;
      background-size: contain;
      background-repeat: no-repeat;
      content: '';
      display: block;
      position: absolute;

      //top: 15px;
      //left: 0;
      //transform: translate(-100px, -50px) rotateZ(55deg);
    }
  }

  // &.introjs-left .introjs-arrow {
  // 	top: 0;
  // 	left: 0;
  // 	&:before {
  // 		transform: translate(250px, -70px) rotateY(-175deg) rotateZ(45deg);
  // 	}
  // }
  .introjs-tooltip-header {
    h1.introjs-tooltip-title {
      font: {
        size: 17px;
        family: $font-family-base;
        weight: $font-weight-semibold;
      }
    }
  }

  .introjs-tooltiptext {
    p {
      font: {
        size: 14px;
        family: $font-family-base;
        weight: $font-weight-regular;
      }

      color: #777;
      margin-bottom: 10px;
    }

    padding-top: 5px;

    span {
      i {
        font-family: Hmmngbrd !important;
        color: #25b183;
        font-size: 20px;
      }

      color: #777;
      display: inline-flex;
      font: {
        size: 12px;
        family: $font-family-base;
        weight: $font-weight-regular;
      }
    }
  }

  .introjs-tooltipbuttons {
    border-top: 0;
    text-align: center;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 15px;
    padding-top: 0;

    a {
      &.introjs-nextbutton {
        @include button-success;

        height: 40px !important;
        text-shadow: none;
        padding: 0 18px;

        @include media-breakpoint-down(xl) {
          padding: 0 18px;
        }
      }
    }
  }

  .introjs-prevbutton {
    display: none;
  }

  &.UserGuideCreatePost {
    .introjs-arrow {
      &::before {
        left: 280px;
        transform: rotateZ(-40deg);

        @include media-breakpoint-down(md) {
          left: -170px;
          transform: rotateZ(40deg);
          top: -20px;
        }
      }
    }
  }

  &.UserGuidePinPost {
    .introjs-arrow {
      &::before {
        left: -23px;
        transform: rotateX(-170deg) rotateZ(-115deg);
        top: 4px;

        @include media-breakpoint-down(md) {
          left: -35px;
          transform: rotateX(0) rotateZ(-205deg);
          top: -76px;
        }
      }
    }
  }

  &.UserGuideQuestion {
    .introjs-arrow {
      &::before {
        right: -20px;
        transform: rotateZ(60deg);
        top: 44px;
      }
    }

    @include media-breakpoint-down(md) {
      left: 0 !important;

      .introjs-arrow {
        &::before {
          right: -13px;
          transform: rotateZ(-40deg);
          top: -16px;
        }
      }
    }
  }

  &.UserGuideEvents {
    .introjs-arrow {
      &::before {
        right: -20px;
        transform: rotateZ(60deg);
        top: 44px;
      }
    }

    @include media-breakpoint-down(md) {
      left: -80px !important;

      .introjs-arrow {
        &::before {
          left: -10px;
          transform: rotateZ(-40deg);
          top: -16px;
          right: unset;
        }
      }
    }
  }

  // &.UserGuidePublishPage {
  // 	.introjs-arrow {
  // 		&::before {
  // 			right: -20px;
  // 			transform: rotateZ(60deg);
  // 			top: 24px;
  // 		}
  // 	}
  // 	@include media-breakpoint-down(md) {
  // 		left: -210px !important;
  // 		.introjs-arrow {
  // 			&::before {
  // 				right: -70px;
  // 				transform: rotateZ(150deg);
  // 				bottom: -20px;
  // 				top: unset;
  // 			}
  // 		}
  // 	}
  // }
  // &.UserGuideSharePage {
  // 	.introjs-arrow {
  // 		&::before {
  // 			right: -20px;
  // 			transform: rotateZ(60deg);
  // 			top: 24px;
  // 		}
  // 	}
  // 	@include media-breakpoint-down(md) {
  // 		left: -210px !important;
  // 		.introjs-arrow {
  // 			&::before {
  // 				right: -66px;
  // 				transform: rotateZ(30deg);
  // 				top: -22px;
  // 			}
  // 		}
  // 	}
  // }

  // &.UserGuideCreatePost {
  // 	left: 120px !important;
  // 	top: 0px !important;
  // 	@include media-breakpoint-down(md) {
  // 		left: 50% !important;
  // 		margin-top: -283px !important;
  // 		transform: translateX(-50%) !important;
  // 		bottom: unset !important;
  // 		.introjs-arrow {
  // 			display: block !important;
  // 			&:before {
  // 				top: unset !important;
  // 				bottom: 0 !important;
  // 				left: 50% !important;
  // 				transform: translate(0px, -50px) rotateZ(55deg);
  // 			}
  // 		}
  // 		.introjs-arrow.bottom-middle {
  // 			bottom: -150px;
  // 			left: -8%;
  // 			margin-left: -5px;
  // 		}
  // 	}
  // 	&.tabTooltipClass {
  // 		@media (min-width: 768px) and (max-width: 1199.98px) and (orientation: landscape) {
  // 			transform: translateY(70px) !important;
  // 			.introjs-arrow {
  // 				&:before {
  // 					transform: translate(-100px, -20px) rotate(55deg) !important;
  // 				}
  // 			}
  // 		}
  // 	}
  // }
  // &.UserGuidePinPost {
  // 	// opacity: 1 !important;
  // 	// display: block !important;
  // 	left: -310px !important;
  // 	top: -50px !important;
  // 	height: max-content;

  // 	.introjs-arrow {
  // 		&:before {
  // 			top: -65px !important;
  // 			left: 160px !important;
  // 			transform: translate(45px, -10px) rotateZ(157deg) !important;
  // 		}
  // 	}
  // 	&.tabTooltipClass {
  // 		.introjs-arrow {
  // 			&:before {
  // 				top: -85px !important;
  // 				left: 160px !important;
  // 				transform: translate(45px, -10px) rotateZ(157deg) !important;
  // 			}
  // 		}
  // 		@media (min-width: 768px) and (max-width: 1199.98px) and (orientation: landscape) {
  // 			height: max-content;
  // 			left: -350px !important;
  // 			top: -174px !important;
  // 			.introjs-arrow {
  // 				&:before {
  // 					left: 0px !important;
  // 					-webkit-transform: translate(45px, -10px) rotate(157deg) !important;
  // 					transform: translate(146px, 190px) rotate(317deg) !important;
  // 				}
  // 			}
  // 		}
  // 	}
  // 	@include media-breakpoint-down(md) {
  // 		left: -234px !important;
  // 		top: -355px !important;
  // 		.introjs-arrow {
  // 			top: 108px !important;
  // 			left: 66px !important;
  // 			&:before {
  // 				transform: translate(-83px, 172px) rotateZ(210deg) rotateX(-170deg) !important;
  // 			}
  // 		}
  // 	}
  // }
  // &.UserGuideQuestion {
  // 	// opacity: 1 !important;
  // 	// display: block !important;
  // 	left: 320px !important;
  // 	top: -310px !important;
  // 	height: fit-content;

  // 	.introjs-arrow {
  // 		&:before {
  // 			top: 0px !important;
  // 			left: 0 !important;
  // 			transform: translate(-27px, 198px) rotateZ(-35deg) !important;
  // 		}
  // 	}
  // 	@include media-breakpoint-down(md) {
  // 		left: -30px !important;
  // 		top: -375px !important;
  // 		transform: translateY(0px);
  // 		.introjs-arrow {
  // 			top: 10px !important;
  // 			left: 0 !important;
  // 			&:before {
  // 				transform: translate(-37px, 266px) rotate(105deg) !important;
  // 			}
  // 		}
  // 	}
  // 	&.introjs-top-left-aligned {
  // 		&.tabTooltipClass {
  // 			left: -60px !important;
  // 			top: -373px !important;
  // 			transform: translate(0px, 0px);
  // 			.introjs-arrow:before {
  // 				top: 0px !important;
  // 				left: 0 !important;
  // 				transform: translate(50px, 265px) rotateZ(-255deg) !important;
  // 			}
  // 		}
  // 		.introjs-arrow:before {
  // 			transform: translate(-109px, 108px) rotateX(180deg) rotateZ(45deg) !important;
  // 		}
  // 	}
  // }
  // &.UserGuideEvents {
  // 	// opacity: 1 !important;
  // 	// display: block !important;
  // 	left: 323px !important;
  // 	top: -253px !important;
  // 	height: fit-content;
  // 	.introjs-arrow {
  // 		&.top {
  // 			&::before {
  // 				top: 75px;
  // 				left: -109px;
  // 				transform: translate(0px, 0px) rotateZ(-45deg) rotateX(180deg);
  // 			}
  // 		}
  // 		&.bottom {
  // 			bottom: -212px !important;
  // 			&:before {
  // 				bottom: -212px !important;
  // 				left: -18px !important;
  // 				transform: translate(-92px, -164px) rotateX(-161deg) rotateZ(40deg) !important;
  // 			}
  // 		}
  // 	}
  // 	@include media-breakpoint-down(md) {
  // 		left: -80px !important;
  // 		top: -383px !important;
  // 		transform: translateY(50px);
  // 		.introjs-arrow {
  // 			transform: translate(-90px, 253px) rotateZ(40deg);
  // 			&:before {
  // 				top: 0;
  // 				transform: translate(-94px, -20px) rotate(55deg);
  // 			}
  // 			&.bottom {
  // 				bottom: -212px !important;
  // 			}
  // 		}
  // 	}
  // 	&.tabTooltipClass {
  // 		left: -70px !important;
  // 		top: -433px !important;
  // 		.introjs-arrow.bottom:before {
  // 			bottom: -212px !important;
  // 			left: -18px !important;
  // 			transform: translate(70px, 7px) rotateZ(-255deg) !important;
  // 		}
  // 	}
  // }
  &.UserGuidePublishPage,
  &.UserGuideSharePage {
    // opacity: 1 !important;
    // display: block !important;
    h4 {
      font-size: 18px;
      font-weight: $font-weight-semibold;
      font-family: $font-family-base;
    }

    @include media-breakpoint-up(xl) {
      left: 118px !important;
      top: -56px !important;

      .introjs-arrow {
        &::before {
          top: 7px !important;
          left: -18px !important;
          transform: translate(-83px, -15px) rotateZ(55deg) !important;
        }
      }
    }

    &.tabTooltipClass {
      transform: translate(0, 0);
      bottom: 90px !important;
      left: -105px !important;
      height: max-content;

      .introjs-arrow::before {
        transform: translateY(-90px) rotate(155deg);

        @media (width >= 768px) and (width <= 1199.98px) and (orientation: landscape) {
          transform: translateY(-90px) translateX(-130px) rotate(155deg);
        }
      }
    }

    @include media-breakpoint-down(md) {
      transform: translate(-30px, 20px);

      &.introjs-top-middle-aligned {
        bottom: 80px !important;

        .introjs-arrow {
          display: block !important;

          &::before {
            transform: translateY(170px) translateX(-1px) rotate(39deg) !important;
          }
        }
      }

      .introjs-arrow {
        display: block !important;

        &::before {
          transform: translate(0, -100px) rotateZ(135deg) !important;
        }
      }
    }
  }

  &.UserGuideSharePage {
    .introjs-tooltip-header {
      display: none;
    }

    img {
      margin: 10px 0;
    }

    &.introjs-top-middle-aligned {
      .introjs-arrow {
        &.bottom-middle {
          &::before {
            transform: translateY(230px) translateX(-1px) rotate(39deg) !important;
          }
        }
      }
    }

    &.introjs-bottom-left-aligned {
      top: 0 !important;
      transform: translate(0, 0);

      .introjs-arrow::before {
        top: 37px !important;
        left: -18px !important;
        transform: translate(-83px, 5px) rotateZ(55deg) !important;
      }

      &.tabTooltipClass {
        transform: translate(0, 0);
        bottom: 90px !important;
        left: -105px !important;
        height: max-content;
        top: 70px !important;

        .introjs-arrow::before {
          transform: translate(20px, -115px) rotate(155deg) !important;

          @media (width >= 768px) and (width <= 1199.98px) and (orientation: landscape) {
            transform: translate(-134px, -95px) rotate(149deg) !important;
          }
        }
      }
    }
  }

  &.UserGuideSubscribe {
    left: 118px !important;
    top: -56px !important;

    h4 {
      font-size: 18px;
      font-family: $font-family-base;
      font-weight: $font-weight-semibold;
    }

    .introjs-arrow {
      &::before {
        top: 7px !important;
        left: -18px !important;
        transform: translate(-83px, -15px) rotateZ(55deg) !important;
      }
    }

    @include media-breakpoint-down(md) {
      left: 30px !important;
      top: 4px !important;

      .introjs-arrow {
        display: block !important;

        &::before {
          left: -58px !important;
          top: 177px !important;
          transform: translate(0, -259px) rotateZ(83deg) !important;
        }
      }
    }
  }
}
