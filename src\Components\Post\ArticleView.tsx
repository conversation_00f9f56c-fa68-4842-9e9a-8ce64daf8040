
//@ts-nocheck
import './ArticleView.scss'

import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import ReactQuill from 'react-quill'
import 'react-quill/dist/quill.snow.css'
import 'react-quill/dist/quill.bubble.css'

import { createdMonthDayYear } from '../../commonservice'
import { useAppSelector } from '../../Hooks'
import { loading } from '../../Redux/slices/userSlice'
import { environment } from '../../AppConfig/environment'
import Loader from '../Loader/Loader'
import AudioWaves from '../Shared/AudioWaves'
import Header from '../Header/Header'
import axios from 'axios'

const ArticleView: React.FC = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { postId } = useParams()

  const userData = useAppSelector((state: any) => state?.user)
  const posts = useAppSelector((state) => state.post.posts)

  const [article, setArticle] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  // Define Quill modules - only enable necessary features for viewing
  const [quillModules] = useState({
    toolbar: false,
    clipboard: {
      matchVisual: false
    }
  })

  // Define Quill formats - match the formats used in editor
  const [quillFormats] = useState([
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike',
    'color', 'background',
    'script',
    'blockquote', 'code-block',
    'list', 'bullet', 'indent',
    'direction', 'align',
    'link', 'image', 'video', 'formula'
  ])

  useEffect(() => {
    const fetchArticle = async () => {
      if (!postId) return

      dispatch(loading(true))
      try {
        const token = userData?.value?.token
        const response = await axios.get(
          `${environment.apiEndPoint}page/details/post/${postId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        )

        if (response.data.success && response.data.data?.length > 0) {
          setArticle(response.data.data[0]) // Get first item since it's an array
        } else {
          setError('Article not found')
        }
      } catch (err) {
        console.error('Error fetching article:', err)
        setError('Failed to load article')
      } finally {
        dispatch(loading(false))
      }
    }

    // Find the article from posts first
    const foundArticle = posts?.find((post) => post?._id === postId)
    if (foundArticle) {
      setArticle(foundArticle)
    } else {
      fetchArticle()
    }
  }, [postId, dispatch, userData?.value?.token, posts])

  const renderMedia = (media: any) => {
    if (!media) return null

    if (media.type === 'image') {
      return (
        <div className="article-image">
          <img src={media.url} alt={media.caption || 'Article image'} />
          {media.caption && <p className="caption">{media.caption}</p>}
        </div>
      )
    } else if (media.type === 'video') {
      return (
        <div className="article-video">
          <video src={media.url} controls poster={media.thumbnail}>
            Your browser does not support video playback.
          </video>
          {media.caption && <p className="caption">{media.caption}</p>}
        </div>
      )
    } else if (media.type === 'audio') {
      return (
        <div className="article-audio">
          <AudioWaves
            audioURL={media.url}
            controls
            stopAllMedia={() => {}}
            mediaPlay={false}
          />
          {media.caption && <p className="caption">{media.caption}</p>}
        </div>
      )
    }

    return null
  }

  if (error) {
    return (
      <div className="article-error">
        <h3>{error}</h3>
        <button onClick={() => navigate(-1)} className="back-button">
          <i className="fa fa-leftarrow"></i> Go Back
        </button>
      </div>
    )
  }

  if (!article || userData.loading) {
    return <Loader />
  }

  return (
    <div className="article-view-container">
      <Header />
      <div className="article-content">
        <div className="article-header">
          <button
            onClick={() => navigate(-1)}
            className="go-back-button"
            title="Go Back"
          >
            <i className="fa fa-arrow-left"></i>
          </button>
          <h1>{article?.title || ''}</h1>
          <div className="article-meta">
            {article?.createdBy && (
              <>
                {article.createdBy.image ? (
                  <img src={article.createdBy.image} alt={article.createdBy.name} className="author-pic" />
                ) : (
                  <div className="author-pic author-initial">
                    <span>{article.createdBy.name?.charAt(0)}</span>
                  </div>
                )}
                <span className="author-name">{article.createdBy.name}</span>
              </>
            )}
            <span className="article-date">{createdMonthDayYear(article?.createdAt)}</span>
          </div>
        </div>
        <div className="article-body">
          {article?.description && (
            <div className="article-description">
              <ReactQuill
                value={article.description}
                readOnly={true}
                modules={quillModules}
                formats={quillFormats}
                theme="snow"
              />
            </div>
          )}
          {article?.medias && article.medias.length > 0 && (
            <div className="article-media-content">
              {article.medias.map((media: any, index: number) => (
                <div key={index} className="media-item">
                  {renderMedia(media)}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ArticleView
