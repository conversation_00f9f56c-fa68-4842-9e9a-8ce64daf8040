import { useSelector, shallowEqual } from 'react-redux';
import { RootState } from '../Redux/store';

/**
 * A hook that uses shallowEqual for comparison to prevent unnecessary rerenders
 * when selected redux state hasn't changed meaningfully
 * @param selector - Selector function to get state from redux
 * @returns The selected state
 */
export function useShallowEqualSelector<T>(selector: (state: RootState) => T): T {
  return useSelector(selector, shallowEqual);
}

/**
 * Hook to select part of the state with a lower likelihood of rerenders
 * @param paths - Array of paths to select from state
 * @returns Object with selected paths
 */
export function useSelectorSubset(paths: string[]) {
  return useShallowEqualSelector((state: RootState) => {
    const result: Record<string, any> = {};
    
    paths.forEach(path => {
      const parts = path.split('.');
      let current: any = state;
      
      // Navigate through the path
      for (const part of parts) {
        if (current === undefined) break;
        current = current[part];
      }
      
      // Set the value at the path in the result
      let target = result;
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        target[part] = target[part] || {};
        target = target[part];
      }
      
      target[parts[parts.length - 1]] = current;
    });
    
    return result;
  });
} 