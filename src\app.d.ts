// @ts-nocheck
declare module 'turnstone'
declare module 'turnstone-recent-searches'
declare module 'uuid'
declare module 'react-outside-click-handler'

declare module 'react-step-progress-bar' {
  interface ProgressBarProps {
    percent?: number
    filledBackground?: any
    height?: string | number
    stepPositions?: number
    children?: any
  }

  interface StepProps {
    transition?: any
    position?: any
    children?: any
  }
  class ProgressBar extends React.Component<ProgressBarProps, any> {}
  class Step extends React.Component<StepProps, any> {}
}

declare module 'react-image-video-lightbox' {
  interface ReactImageVideoLightboxProps {
    data?: any
    startIndex?: any
    showResourceCount?: boolean
    onCloseCallback?: any
    onNavigationCallback?: any
  }
  export default class ReactImageVideoLightbox extends React.Component<ReactImageVideoLightboxProps, any> {}
}

declare module 'react-dropbox-chooser' {
  interface ReactDropboxChooserProps {
    appKey?: any
    success?: any
    cancel?: any
    multiselect?: boolean
    extensions?: any
    children?: any
  }
  export default class ReactDropboxChooser extends React.Component<ReactDropboxChooserProps, any> {}
}
