<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 60 60">
  <defs>
    <style>
      .cls-1 {
        fill: #00a87a;
      }

      .cls-2 {
        fill: #fff;
        filter: url(#drop-shadow-3);
      }
    </style>
    <filter id="drop-shadow-3" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur" stdDeviation="4"/>
      <feFlood flood-color="#000" flood-opacity=".15"/>
      <feComposite in2="blur" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <circle class="cls-2" cx="30" cy="30" r="25"/>
  <path class="cls-1" d="m25,39.58v-19.17c0-.69.78-1.08,1.33-.67l12.78,9.58c.44.33.44,1,0,1.33l-12.78,9.58c-.55.41-1.33.02-1.33-.67Z"/>
</svg>