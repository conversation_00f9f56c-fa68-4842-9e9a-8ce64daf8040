{"name": "livinglegacy", "version": "0.1.0", "private": true, "dependencies": {"@aws-sdk/client-s3": "^3.373.0", "@aws-sdk/lib-storage": "^3.373.0", "@react-google-maps/api": "^2.18.1", "@reduxjs/toolkit": "^1.9.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/google-map-react": "^2.1.7", "@types/jest": "^27.5.2", "@types/socket.io-client": "^3.0.0", "@types/uuid": "^9.0.2", "axios": "^1.3.4", "bootstrap": "^5.2.3", "classnames": "^2.3.2", "content-disposition": "^0.5.4", "countries-and-timezones": "^3.4.1", "date-fns": "^2.30.0", "emoji-picker-react": "^4.4.7", "formik": "^2.2.9", "framer-motion": "^10.12.9", "g": "^2.0.1", "google-map-react": "^2.2.1", "husky-init": "^8.0.0", "intro.js": "^7.0.1", "intro.js-react": "^1.0.0", "jose": "^4.13.1", "jquery": "^3.7.0", "jsonwebtoken": "^9.0.0", "lottie-react": "^2.4.0", "react": "^18.2.0", "react-bootstrap": "^2.7.2", "react-datepicker": "^4.11.0", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-dropbox-chooser": "^0.0.5", "react-dropdown-select": "^4.9.3", "react-easy-crop": "^4.7.4", "react-fb-image-video-grid": "^0.1.10", "react-ga4": "^2.1.0", "react-google-drive-picker": "^1.2.2", "react-google-recaptcha-v3": "^1.10.1", "react-helmet-async": "^1.3.0", "react-image-video-lightbox": "^3.0.1", "react-media-recorder": "^1.6.5", "react-multi-carousel": "^2.8.4", "react-multi-email": "^1.0.9", "react-nl2br": "^1.0.4", "react-outside-click-handler": "^1.3.0", "react-phone-number-input": "^3.2.22", "react-photo-album": "^2.2.2", "react-qrcode-logo": "^2.9.0", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.9.0", "react-scripts": "^5.0.1", "react-select": "^5.7.3", "react-share": "^4.4.1", "react-simply-carousel": "^9.0.2", "react-slick": "^0.29.0", "react-spring-3d-carousel": "^1.3.4", "react-step-progress-bar": "^1.0.3", "react-tagsinput": "^3.20.1", "react-textarea-autosize": "^8.5.2", "react-with-gesture": "^4.0.8", "realm-web": "^2.0.0", "redux-persist": "^6.0.0", "redux-persist-transform-encrypt": "^5.0.0", "sass": "^1.64.1", "slick-carousel": "^1.8.1", "socket.io-client": "^4.6.1", "turnstone": "^2.2.0", "turnstone-recent-searches": "^0.5.0", "typescript": "^4.9.5", "uuid": "^9.0.0", "wavesurfer.js": "^6.6.3", "web-vitals": "^2.1.4", "yup": "^1.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "pm2 serve --port 8000 build --name butterfly --spa", "docker:compose": "docker-compose up", "deploy:serve": "npm i serve && serve -s .", "deploy:build": "npm run build && docker-compose build", "deploy:kill": "docker-compose down", "deploy:tag": "docker tag app registry.digitalocean.com/butterflyv2-stage/app", "deploy:push": "docker push registry.digitalocean.com/butterflyv2-stage/app", "deploy": "npm run deploy:build && npm run deploy:tag && npm run deploy:push", "format": "yarn prettier . --write", "pr:create": "gh pr create --base development-v3", "nserver": "nodemon server/index.js", "lint": "eslint \"src/**/*.{ts,tsx,js,jsx}\"", "lint:fix": "eslint \"src/**/*.{ts,tsx,js,jsx}\" --fix", "style": "stylelint \"**/*.scss\"", "style:fix": "stylelint \"**/*.scss\" --fix", "postinstall": "husky install"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@react-oauth/google": "^0.9.0", "@types/jquery": "^3.5.17", "@types/jsonwebtoken": "^9.0.1", "@types/node": "^18.15.11", "@types/react": "^18.2.8", "@types/react-datepicker": "^4.15.0", "@types/react-dom": "^18.2.4", "@types/react-slick": "^0.23.10", "@types/wavesurfer.js": "^6.0.5", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-config-standard-with-typescript": "^39.0.0", "eslint-plugin-n": "^16.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.33.0", "eslint-plugin-simple-import-sort": "^10.0.0", "husky": "^8.0.0", "lint-staged": "^14.0.1", "nodemon": "^2.0.22", "prettier": "3.0.3", "serve": "^14.2.0", "stylelint": "^15.10.3", "stylelint-config-standard-scss": "^11.0.0", "vite-plugin-checker": "^0.6.1", "vite-plugin-handlebars": "^1.6.0", "vite-plugin-svgr": "^3.2.0", "vite-tsconfig-paths": "^4.2.0"}, "resolutions": {"@types/react": "...", "@types/react-dom": "..."}, "lint-staged": {"**/*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "**/*.{css,scss}": ["stylelint --fix"]}}