<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="referrer" content="origin" />
  <!-- <link rel="icon" href="%PUBLIC_URL%/favicon.ico" /> -->
  <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" /> -->
  <meta name='viewport'
    content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, target-densityDpi=device-dpi, minimal-ui' />

  <!-- 
  <meta httpEquiv="x-ua-compatible" content="ie=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <meta http-equiv="X-Frame-Options" content="deny" /> -->

  <meta name="theme-color" content="#000000" />
  <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
  <meta name="url" content="https://butterfly.co/" />
  <meta name="title" content="Living Legacy" />
  <meta name="description"
    content="Create a page that allows you to remember and share the stories, pictures, and videos of the important moments in your life." />
  <meta name="image" content="https://butterfly-staging-assets.nyc3.cdn.digitaloceanspaces.com/image/page-share.jpg" />

  <meta property="og:title" content="Butterfly" />
  <meta property="og:image"
    content="https://butterfly-staging-assets.nyc3.cdn.digitaloceanspaces.com/image/page-share.jpg" />
  <meta property="og:url" content="https://butterfly.co/" />
  <meta property="og:description"
    content="Create a page that allows you to remember and share the stories, pictures, and videos of the important moments in your life." />

  <meta name="twitter:creator" content="Butterfly" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Butterfly" />
  <meta name="twitter:description"
    content="Create a page that allows you to remember and share the stories, pictures, and videos of the important moments in your life." />
  <meta name="twitter:image"
    content="https://butterfly-staging-assets.nyc3.cdn.digitaloceanspaces.com/image/page-share.jpg" />

  <meta name="robots" content="index,follow" />

  <script src="https://use.fortawesome.com/b5e0e19b.js"></script>
  <meta http-equiv="ScreenOrientation" content="autoRotate:disabled">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap"
    rel="stylesheet">

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-CBVBQNETCM"></script>
  <script src="https://hello.pledge.to/assets/widget.js" async="async"></script>
  <!-- <script src="https://staging.pledge.to/assets/widget.js" id="plg-widget" async></script> -->
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-CBVBQNETCM'); 
  </script>
  <title>Living Legacy</title>
  <style>
    #turn {
      display: none;

      /* opacity: 0;
       visibility: hidden;
       height: 0;
       width: 0; */

    }

    #turn p {
      font-size: 0;
    }

  </style>
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <div id="turn">
    <p>
      <span><img src="https://hmmngbrd.nyc3.cdn.digitaloceanspaces.com/upload%2Flanding_page%2Fphone-rotate.png"
          alt=""></span>
      Please rotate your device to portrait <br />for better experience!
    </p>
  </div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.
      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.
      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
</body>

</html>