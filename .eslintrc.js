module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
  },
  rules: {
    // Disable all rules
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-empty-function': 'off',
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    // Disable all other rules
    'prettier/prettier': 'off',
  },
  // This disables all rules entirely
  ignorePatterns: ['**/*'],
} 