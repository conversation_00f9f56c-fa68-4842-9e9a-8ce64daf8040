// @ts-nocheck
import 'intro.js/introjs.css'
import './LayoutContainer.scss'

import cn from 'classnames'
import { Steps } from 'intro.js-react'
import { useEffect, useState } from 'react'
import { Container } from 'react-bootstrap'
import { isMobile } from 'react-device-detect'
import { useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'

import { logoNoText } from '../../commonservice'
import { useAppSelector } from '../../Hooks'
import { setAppError, setPopup, setSidePanel } from '../../Redux/slices/metaSlice'
import { addPageAction, pageReturnAction, setActiveSideSlug } from '../../Redux/slices/pageDetailSlice'
import { setShowStepStatus } from '../../Redux/slices/userSlice'
import { updateUserInfo } from '../../Redux/thunks/settingsThunk'
import AddDeleteQuestion from '../Modals/AddDeleteQuestion/AddDeleteQuestion'
import AlertPopup from '../Modals/AlertPopup/AlertPopup'
import DeletePopup from '../Modals/DeletePopup'
import { NavComponent } from '../Nav'
import QuestionsPopup from '../QuestionsPopup'
import SidePanel from '../SidePanel/SidePanel'
import {
  UserGuidecreatePost,
  UserGuideEvents,
  UserGuidePinPost,
  UserGuidePublishPost,
  UserGuideQuestion,
  UserGuideSharePost,
  UserGuideSubscribe,
  UserGuideSubscribePending,
} from '../UserGuide/UserGuide'

export function LayoutContainer(props: any) {
  const navigate = useNavigate()
  const [, setIsActive] = useState(false)
  const [deleteshow, setDeleteshow] = useState(false)
  const handleDeletesPopupClose = () => {
    setDeleteshow(false)
  }
  const dispatch = useDispatch()
  const [deleteType, setDeleteType] = useState('page')

  const appMeta = useAppSelector(state => state.metadata)
  const pageDetailState = useAppSelector((state: any) => state.pagedetail)
  const userState = useAppSelector(state => state.user)
  const pageDetailReturnAction = pageDetailState?.returnAction
  const pageAction = pageDetailState?.pageAction
  const pageData = pageDetailState.data
  const [stepDone, setStepDone] = useState(0)
  const [showQuestionStep, setShowQuestionStep] = useState(true)
  const [showSubsciption, setShowSubsciption] = useState(false)
  const [cantScroll, setCantScroll] = useState(false)

  const handleClose = (e?: any) => {
    setIsActive(current => !current)
    dispatch(
      setSidePanel({
        panelBody: null,
        status: false,
      })
    )
    setTimeout(() => {
      dispatch(setActiveSideSlug(''))
    }, 400)
  }
  const [stepsStatus, setStepStatus] = useState(false)

  const [currentStep, setCurrentStep] = useState(0)

  const steps = [
    {
      element: '.CreatePostBtn',
      title: 'Create a post',
      intro: <UserGuidecreatePost />,
      tooltipClass: 'UserGuideCreatePost myTooltipClass',
    },

    {
      element: '.DesktopQuestion',
      title: 'It all starts with Questions',
      intro: <UserGuideQuestion />,
      tooltipClass: 'UserGuideQuestion myTooltipClass',
      hidePrev: true,
      nextLabel: 'Got it!',
      position: 'right',
    },
    {
      element: '.fa-butterflypin',
      title: 'Is your post ready for pinning?',
      intro: <UserGuidePinPost />,
      tooltipClass: 'UserGuidePinPost myTooltipClass',
      nextLabel: 'Got it!',
      position: 'left',
    },
    {
      element: '.EventsModule',
      title: 'Your Events',
      intro: <UserGuideEvents />,
      tooltipClass: 'UserGuideEvents myTooltipClass',
      nextLabel: 'Got it!',
      position: 'right',
      hidePrev: true,
    },

    {
      element: '.publishButton',
      title: 'Publish your page',
      intro: <UserGuidePublishPost />,
      tooltipClass: 'UserGuidePublishPage myTooltipClass',
      nextLabel: 'Got it!',
      position: 'right',
    },
    {
      element: '.shareButton',
      intro: <UserGuideSharePost />,
      tooltipClass: 'UserGuideSharePage myTooltipClass',
      showButtons: false,
      position: 'right',
    },
  ]

  const stepsMobile = [
    {
      element: '.CreatePostFixed',
      title: 'Create a post',
      intro: <UserGuidecreatePost />,
      tooltipClass: 'UserGuideCreatePost myTooltipClass',
    },

    {
      element: '.MobileFeatureNav .MobileQuestion',
      title: 'It all starts with Questions',
      intro: <UserGuideQuestion />,
      tooltipClass: 'UserGuideQuestion myTooltipClass',
      hidePrev: true,
      nextLabel: 'Got it!',
      position: 'top',
    },
    {
      element: '.fa-butterflypin',
      title: 'Is your post ready for pinning?',
      intro: <UserGuidePinPost />,
      tooltipClass: 'UserGuidePinPost myTooltipClass',
      nextLabel: 'Got it!',
    },
    {
      element: '.MobileFeatureNav .EventsModule',
      title: 'Your Events',
      intro: <UserGuideEvents />,
      tooltipClass: 'UserGuideEvents myTooltipClass',
      nextLabel: 'Got it!',
      position: 'bottom',
      hidePrev: true,
    },

    {
      element: '.CenterSection .publishButton',
      title: 'Publish your page',
      intro: <UserGuidePublishPost />,
      tooltipClass: 'UserGuidePublishPage myTooltipClass',
      nextLabel: 'Got it!',
      position: 'top',
    },
    {
      element: '.CenterSection .shareButton',
      intro: <UserGuideSharePost />,
      tooltipClass: 'UserGuideSharePage myTooltipClass',
      showButtons: false,
      position: 'top',
    },
  ]

  const stepsTabSmallSize = [
    {
      element: '.CenterSection .CreatePostBtn',
      title: 'Create a post',
      intro: <UserGuidecreatePost />,
      tooltipClass: 'UserGuideCreatePost myTooltipClass tabTooltipClass',
    },

    {
      element: '.MobileFeatureNav .MobileQuestion',
      title: 'It all starts with Questions',
      intro: <UserGuideQuestion />,
      tooltipClass: 'UserGuideQuestion myTooltipClass tabTooltipClass',
      hidePrev: true,
      nextLabel: 'Got it!',
      position: 'bottom',
    },
    {
      element: '.fa-butterflypin',
      title: 'Is your post ready for pinning?',
      intro: <UserGuidePinPost />,
      tooltipClass: 'UserGuidePinPost myTooltipClass tabTooltipClass',
      nextLabel: 'Got it!',
      position: 'top',
    },
    {
      element: '.MobileFeatureNav .EventsModule',
      title: 'Your Events',
      intro: <UserGuideEvents />,
      tooltipClass: 'UserGuideEvents myTooltipClass tabTooltipClass',
      nextLabel: 'Got it!',
      position: 'bottom',
      hidePrev: true,
    },

    {
      element: '.CenterSection .publishButton',
      title: 'Publish your page',
      intro: <UserGuidePublishPost />,
      tooltipClass: 'UserGuidePublishPage myTooltipClass tabTooltipClass',
      nextLabel: 'Got it!',
      position: 'bottom',
    },
    {
      element: '.CenterSection .shareButton',
      intro: <UserGuideSharePost />,
      tooltipClass: 'UserGuideSharePage myTooltipClass tabTooltipClass',
      showButtons: false,
      position: 'bottom',
    },
  ]

  const stepsSubsciption = [
    {
      element: '.subscribe-badge',
      title: 'Join',
      intro: <UserGuideSubscribe />,
      tooltipClass: 'UserGuideSubscribe myTooltipClass',
    },
  ]

  const stepsSubsciptionMobile = [
    {
      element: '.CenterSection .subscribe-badge',
      title: 'Join',
      intro: <UserGuideSubscribe />,
      tooltipClass: 'UserGuideSubscribe myTooltipClass',
    },
  ]

  const stepsSubsciptionPending = [
    {
      element: '.subscribe-badge',
      title: 'Request Pending',
      intro: <UserGuideSubscribePending />,
      tooltipClass: 'UserGuideSubscribe myTooltipClass',
    },
  ]

  const stepsSubsciptionMobilePending = [
    {
      element: '.CenterSection .subscribe-badge',
      title: 'Request Pending',
      intro: <UserGuideSubscribePending />,
      tooltipClass: 'UserGuideSubscribe myTooltipClass',
    },
  ]

  useEffect(() => {
    setCantScroll(stepsStatus)
  }, [stepsStatus])

  const onChange = (nextStepIndex: any) => {
    if (nextStepIndex === 1 && stepDone === 0) {
      setStepDone(1)
      setStepStatus(false)
      dispatch(
        setShowStepStatus({
          ...userState?.value,
          data: { ...userState?.value?.data, step: '3' },
        })
      )
      const payload = {
        _id: userState?.value?.data?._id,
        step: '3',
      }
      dispatch(updateUserInfo(payload, false))
      dispatch(addPageAction('show-description-post'))
    } else if (nextStepIndex === 2 && stepDone === 1) {
      setStepDone(2)
      setStepStatus(false)
      if (isMobile) {
        dispatch(setActiveSideSlug('question-page'))
        dispatch(
          setSidePanel({
            panelBody: `Your Questions`,
            status: true,
          })
        )
      } else {
        dispatch(setPopup('question-popup'))
      }
      dispatch(
        setShowStepStatus({
          ...userState?.value,
          data: { ...userState?.value?.data, step: '4' },
        })
      )
      const payload = {
        _id: userState?.value?.data?._id,
        step: '4',
      }
      dispatch(updateUserInfo(payload, false))
    } else if (nextStepIndex === 3 && stepDone === 2) {
      setStepDone(3)
      setStepStatus(false)
      if (showQuestionStep) {
        dispatch(addPageAction('show-question-steps'))
      }
    } else if (nextStepIndex === 4 && stepDone === 3) {
      setStepDone(4)
      setStepStatus(false)
      dispatch(setActiveSideSlug('event-page'))
      dispatch(
        setSidePanel({
          panelBody: `Your events`,
          status: true,
        })
      )
      dispatch(
        setShowStepStatus({
          ...userState?.value,
          data: { ...userState?.value?.data, step: '5' },
        })
      )
      const payload = {
        _id: userState?.value?.data?._id,
        step: '5',
      }
      dispatch(updateUserInfo(payload, false))
    } else if (nextStepIndex === 5 && stepDone === 4) {
      setStepDone(4)
      setStepStatus(false)
      dispatch(
        setShowStepStatus({
          ...userState?.value,
          data: { ...userState?.value?.data, step: '6' },
        })
      )
      const payload = {
        _id: userState?.value?.data?._id,
        step: '6',
      }
      dispatch(updateUserInfo(payload, false))
    }
  }

  const onCompleteSubsciption = () => {
    setShowSubsciption(false)
  }

  const onComplete = () => {
    // should be comment(balaji)
    setCantScroll(false)
    dispatch(
      setShowStepStatus({
        ...userState?.value,
        data: { ...userState?.value?.data, showStep: false, step: '' },
      })
    )
    // should be comment(balaji)
    const payload = {
      _id: userState?.value?.data?._id,
      step: '',
      showStep: false,
    }
    // should be comment(balaji)
    dispatch(updateUserInfo(payload, false))
    // should be comment(balaji)
  }

  useEffect(() => {
    if (pageDetailReturnAction === 'back-to-mypage') {
      dispatch(pageReturnAction(''))
      navigate('/mypages')
    }
  }, [pageDetailReturnAction])

  useEffect(() => {
    if (pageData?._id && pageData?.belongsTo?._id === userState?.value?.data?._id && !pageData?.is_published) {
      if (pageAction === 'show-steps') {
        if (userState?.value?.data?.showStep && userState?.value?.data?.step === '1') {
          setCurrentStep(0)
          setStepStatus(true)
        }
        dispatch(addPageAction(''))
      } else if (pageAction === 'show-question-steps' && userState?.value?.data?.step === '3') {
        if (userState?.value?.data?.showStep) {
          setStepDone(1)
          setCurrentStep(1)
          setStepStatus(true)
          setShowQuestionStep(false)
        }
        dispatch(addPageAction(''))
      } else if (pageAction === 'show-pin-steps') {
        if (userState?.value?.data?.showStep) {
          setStepDone(2)
          setCurrentStep(2)
          setStepStatus(true)
        }
        dispatch(addPageAction(''))
      } else if (pageAction === 'show-event-steps' && userState?.value?.data?.step === '4') {
        if (userState?.value?.data?.showStep) {
          setStepDone(3)
          setCurrentStep(3)
          setStepStatus(true)
        }
        dispatch(addPageAction(''))
      } else if (pageAction === 'show-publish-steps' && userState?.value?.data?.step === '5') {
        if (userState?.value?.data?.showStep) {
          setStepDone(4)
          setCurrentStep(4)
          setStepStatus(true)
        }
        dispatch(addPageAction(''))
      }
    }

    if (pageAction === 'hide-steps') {
      setStepStatus(false)
      dispatch(addPageAction(''))
    }
    if (pageData?._id && pageData?.belongsTo?._id === userState?.value?.data?._id && pageData?.is_published) {
      if (pageAction === 'show-share-steps' && userState?.value?.data?.step === '6') {
        if (userState?.value?.data?.showStep) {
          setStepDone(5)
          setCurrentStep(5)
          setStepStatus(true)
        }
        dispatch(addPageAction(''))
      }
    }

    if (
      pageData?._id &&
      pageData?.belongsTo?._id !== userState?.value?.data?._id &&
      (pageData?.subscribtion_status === '' || pageData?.subscribtion_status === 'PENDING')
    ) {
      if (pageAction === 'show-subsciption-step') {
        setShowSubsciption(true)
        dispatch(addPageAction(''))
      }
    }
  }, [pageAction, pageData])

  const getSubscribeStatusStpes = () => {
    if (pageData?.subscribtion_status === 'PENDING') {
      return isMobile ? stepsSubsciptionMobilePending : stepsSubsciptionPending
    } else {
      return isMobile ? stepsSubsciptionMobile : stepsSubsciption
    }
  }

  const getMainStpes = () => {
    if (window.innerWidth > 767.98 && window.innerWidth <= 1199.98) {
      return stepsTabSmallSize
    } else {
      return isMobile ? stepsMobile : steps
    }
  }

  return (
    <>
      {appMeta.error?.status && (
        <AlertPopup
          buttonText={false}
          show={!!appMeta.error.status}
          content={appMeta.error.content}
          state={appMeta.error.state}
          onHide={() => {
            dispatch(setAppError(null))
          }}
        />
      )}
      <section
        id="Layout"
        className={cn('', {
          'scroll-not-allowd': cantScroll,
        })}
      >
        <Container>
          <div className="layoutwrapper">
            <aside>
              <div className="logo">
                <img
                  onClick={() => {
                    navigate('/mypages')
                    handleClose()
                  }}
                  src={logoNoText}
                  alt=""
                />
              </div>
              <NavComponent handleClose={() => {}} />
            </aside>
            <main>
              <div className="MainSection">{props.children}</div>
            </main>

            {(() => {
              switch (appMeta?.popupType) {
                case 'question-popup':
                  return <QuestionsPopup />
                case 'question-add-popup':
                  return <AddDeleteQuestion show={true} handleClose={() => dispatch(setPopup('question-popup'))} />
                default:
                  return null
              }
            })()}
          </div>
        </Container>
        <SidePanel
          Active={appMeta.sidepanel.status}
          handleClose={() => {
            handleClose()
          }}
          setDeleteshow={setDeleteshow}
          setDeleteType={setDeleteType}
        />
      </section>
      {userState?.value?.data?.showStep && (
        <Steps
          enabled={stepsStatus}
          steps={getMainStpes()}
          initialStep={currentStep}
          onComplete={onComplete}
          onExit={() => {}}
          options={{
            showButtons: true,
            exitOnEsc: false,
            exitOnOverlayClick: false,
            keyboardNavigation: false,
            showBullets: false,
            hidePrev: true,
            autoPosition: true,
            disableInteraction: true,
            nextLabel: 'Got it!',
          }}
          onChange={onChange}
        />
      )}

      <Steps
        enabled={showSubsciption}
        steps={getSubscribeStatusStpes()}
        initialStep={currentStep}
        onComplete={onCompleteSubsciption}
        onExit={() => {}}
        options={{
          showButtons: true,
          exitOnEsc: false,
          exitOnOverlayClick: false,
          keyboardNavigation: false,
          showBullets: false,
          hidePrev: true,
          autoPosition: true,
          disableInteraction: true,
          nextLabel: 'Got it!',
        }}
        onChange={() => {}}
      />

      {deleteshow ? (
        <DeletePopup
          deleteshow={deleteshow}
          pageid={pageData?._id}
          handleclose={() => {
            handleDeletesPopupClose()
          }}
          deleteType={deleteType}
          mediadata={pageData}
          albumdata={{}}
        />
      ) : (
        <></>
      )}
    </>
  )
}
