import React, { useState, useEffect, useRef } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * LazyImage component that uses IntersectionObserver to load images
 * only when they come into viewport, saving bandwidth and improving performance
 */
const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className,
  placeholder = 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"%3E%3Crect width="100" height="100" fill="%23cccccc"/%3E%3C/svg%3E',
  style,
  onLoad,
  onError,
}) => {
  const [imageSrc, setImageSrc] = useState<string>(placeholder);
  const [imageLoaded, setImageLoaded] = useState<boolean>(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    // Skip if the image is already loaded or there's no ref
    if (imageLoaded || !imgRef.current) return;
    
    // Disconnect previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create a new IntersectionObserver
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        // If the image is in view
        if (entry.isIntersecting) {
          // Start loading the image
          setImageSrc(src);
          // Stop observing the image
          observer.unobserve(entry.target);
        }
      });
    }, {
      root: null, // Use the viewport
      rootMargin: '50px', // Load images a bit before they come into view
      threshold: 0.1, // Trigger when at least 10% of the image is visible
    });

    // Start observing the image
    observer.observe(imgRef.current);
    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [src, imageLoaded]);

  const handleLoad = () => {
    setImageLoaded(true);
    if (onLoad) onLoad();
  };

  const handleError = () => {
    if (!imageLoaded) {
      setImageSrc(placeholder);
    }
    if (onError) onError();
  };

  return (
    <img
      ref={imgRef}
      src={imageSrc}
      alt={alt}
      className={className}
      style={{
        transition: 'opacity 0.3s',
        opacity: imageLoaded ? 1 : 0.5,
        ...style,
      }}
      onLoad={handleLoad}
      onError={handleError}
    />
  );
};

export default React.memo(LazyImage); 