import React, { useEffect, useRef } from 'react';

interface PerformanceMonitorProps {
  componentName?: string;
  enabled?: boolean;
}

/**
 * Component to monitor render performance of its children
 * Logs render counts and render times to help identify performance issues
 */
const PerformanceMonitor: React.FC<PerformanceMonitorProps & { children: React.ReactNode }> = ({ 
  componentName = 'Component',
  enabled = process.env.NODE_ENV === 'development',
  children 
}) => {
  const renderCount = useRef(0);
  const lastRender = useRef(performance.now());
  const renderTimes = useRef<number[]>([]);

  useEffect(() => {
    if (!enabled) return;

    renderCount.current += 1;
    const currentTime = performance.now();
    const renderTime = currentTime - lastRender.current;
    renderTimes.current.push(renderTime);

    // Log only every 5 renders or if render time is excessive
    if (renderCount.current % 5 === 0 || renderTime > 50) {
      console.log(
        `%c[${componentName}] rendered ${renderCount.current} times. ` +
        `Last render took: ${renderTime.toFixed(2)}ms. ` +
        `Avg: ${(renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length).toFixed(2)}ms`,
        'color: #00b3e6'
      );
    }
    
    lastRender.current = currentTime;

    // Keep only the last 20 render times
    if (renderTimes.current.length > 20) {
      renderTimes.current = renderTimes.current.slice(-20);
    }
  });

  return <>{children}</>;
};

export default PerformanceMonitor;

/**
 * HOC to wrap a component with performance monitoring
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  monitorOptions: PerformanceMonitorProps = {}
): React.FC<P> {
  const WithPerformanceMonitoring: React.FC<P> = (props) => {
    return (
      <PerformanceMonitor {...monitorOptions} componentName={Component.displayName || Component.name}>
        <Component {...props} />
      </PerformanceMonitor>
    );
  };

  WithPerformanceMonitoring.displayName = `WithPerformanceMonitoring(${Component.displayName || Component.name})`;
  
  return WithPerformanceMonitoring;
} 