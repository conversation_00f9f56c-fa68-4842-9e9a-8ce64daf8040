// @ts-nocheck
import React, { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

/**
 *
 * @param authState The boolean state returned by useAuth()
 * @param children props.children type or react component
 * @returns {React.PropsWithChildren | void}
 */

export const AuthHandler = (props: any) => {
  const navigate = useNavigate()

  useEffect(() => {
    if (!props.authState) {
      navigate('/signin')
    }
  }, [props.authState])

  return <>{props.children}</>
}
