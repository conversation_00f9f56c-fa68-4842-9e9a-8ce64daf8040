// Gray scale variables
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;

// Grid breakpoints
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/mixins';

.profile-page {
  min-height: 100vh;
  background: $gray-100;
  width: 100%;
  overflow-y: auto;

  .profile-header-bar {
    background: $white;
    padding: 1rem 2rem;
    border-bottom: 1px solid $gray-300;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 4px rgba($gray-900, 0.1);

    @media (max-width: map-get($grid-breakpoints, md)) {
      padding: 1rem;
      flex-direction: column;
      gap: 1rem;
      position: relative;
    }

    .back-button {
      background: transparent;
      border: 1px solid $gray-400;
      color: $gray-700;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover {
        background: $gray-100;
        border-color: $gray-500;
      }

      i {
        font-size: 1rem;
      }

      @media (max-width: map-get($grid-breakpoints, md)) {
        align-self: flex-start;
      }
    }

    h1 {
      font-size: 1.8rem;
      font-weight: 600;
      color: $gray-800;
      margin: 0;

      @media (max-width: map-get($grid-breakpoints, md)) {
        font-size: 1.5rem;
        align-self: center;
      }
    }

    .edit-profile-btn {
      background: transparent;
      border: 1px solid $success;
      color: $success;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      transition: all 0.2s ease;

      &:hover {
        background: $success;
        color: $white;
      }

      i {
        font-size: 1rem;
      }

      @media (max-width: map-get($grid-breakpoints, md)) {
        align-self: flex-end;
      }
    }
  }

  .profile-body {
    padding: 0;
    flex: 1;
  }

  .profile-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: calc(100vh - 80px); // Ensure content takes full height

    @media (max-width: map-get($grid-breakpoints, lg)) {
      max-width: 100%;
    }

    @media (max-width: map-get($grid-breakpoints, md)) {
      padding: 1rem;
    }

    .profile-header {
      background: $white;
      padding: 2rem;
      border-radius: 12px;
      display: flex;
      align-items: center;
      gap: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 8px rgba($gray-900, 0.08);

      @media (max-width: map-get($grid-breakpoints, md)) {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
        gap: 1rem;
      }

      .profile-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        background: $gray-200;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .profile-initial {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 3rem;
          color: $success;
          background: #e8f5e9;
        }

        @media (max-width: map-get($grid-breakpoints, md)) {
          width: 100px;
          height: 100px;
        }

        @media (max-width: map-get($grid-breakpoints, sm)) {
          width: 80px;
          height: 80px;
        }
      }

      .profile-info {
        h2 {
          font-size: 1.8rem;
          margin-bottom: 0.5rem;
          color: $gray-800;

          @media (max-width: map-get($grid-breakpoints, md)) {
            font-size: 1.5rem;
          }

          @media (max-width: map-get($grid-breakpoints, sm)) {
            font-size: 1.3rem;
          }
        }

        .email {
          font-size: 1rem;
          color: $gray-600;
          margin: 0;
        }
      }
    }

    .qa-section {
      background: $white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba($gray-900, 0.08);
      margin-bottom: 2rem;

      @media (max-width: map-get($grid-breakpoints, md)) {
        padding: 1.5rem;
      }

      @media (max-width: map-get($grid-breakpoints, sm)) {
        padding: 1rem;
      }

      h3 {
        font-size: 1.4rem;
        margin-bottom: 1.5rem;
        color: $gray-800;
        font-weight: 600;

        @media (max-width: map-get($grid-breakpoints, md)) {
          font-size: 1.2rem;
        }
      }

      .qa-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
        
        @media (max-width: map-get($grid-breakpoints, sm)) {
          grid-template-columns: 1fr;
        }
      }

      .qa-card {
        background: $gray-100;
        padding: 1.5rem;
        border-radius: 12px;
        transition: all 0.2s ease;
        border: 1px solid $gray-200;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba($gray-900, 0.1);
        }

        h4 {
          font-size: 1.1rem;
          color: $gray-800;
          margin-bottom: 1rem;
          font-weight: 500;
          border-bottom: 2px solid $success;
          padding-bottom: 0.5rem;

          @media (max-width: map-get($grid-breakpoints, md)) {
            font-size: 1rem;
          }
        }

        p {
          font-size: 1rem;
          color: $gray-600;
          margin: 0;
          padding: 0.5rem;
          border-radius: 4px;
          position: relative;
          cursor: text;
          min-height: 60px;

          &:hover {
            background: $white;

            .edit-icon {
              opacity: 1;
            }
          }

          .edit-icon {
            position: absolute;
            right: 0.5rem;
            top: 0.5rem;
            color: $success;
            opacity: 0;
            transition: opacity 0.2s ease;
          }

          @media (max-width: map-get($grid-breakpoints, md)) {
            font-size: 0.9rem;
          }
        }

        textarea {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid $gray-300;
          border-radius: 4px;
          font-size: 1rem;
          color: $gray-800;
          resize: vertical;
          min-height: 60px;

          &:focus {
            outline: none;
            border-color: $success;
          }

          @media (max-width: map-get($grid-breakpoints, md)) {
            font-size: 0.9rem;
          }
        }
      }
    }

    .center-section-wrapper {
      background: $white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba($gray-900, 0.08);
      margin-bottom: 2rem; // Add bottom margin for better spacing

      @media (max-width: map-get($grid-breakpoints, md)) {
        padding: 1.5rem;
      }

      @media (max-width: map-get($grid-breakpoints, sm)) {
        padding: 1rem;
      }

      h3 {
        font-size: 1.4rem;
        margin-bottom: 1.5rem;
        color: $gray-800;
        font-weight: 600;

        @media (max-width: map-get($grid-breakpoints, md)) {
          font-size: 1.2rem;
        }
      }

      .center-section-content {
        .CenterSection {
          border: none;
          padding: 0;
          max-height: none;
          overflow: visible;
        }
      }
    }
  }
}
