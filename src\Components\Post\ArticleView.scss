@import 'bootstrap/scss/bootstrap';
@import 'bootstrap/scss/mixins';
@import '../../Assets/Styles/typography';
@import '../../Assets/Styles/colors';
@import '../../Assets/Styles/mixins';

.ArticleViewContainer {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;

  .back-navigation {
    margin-bottom: 20px;

    .back-button {
      background: none;
      border: none;
      color: $success;
      font-family: $font-family-base;
      font-weight: $font-weight-medium;
      font-size: 16px;
      padding: 0;
      display: flex;
      align-items: center;
      cursor: pointer;

      i {
        margin-right: 8px;
        font-size: 18px;
      }

      &:hover {
        color: $hover-button;
      }
    }
  }

  .ArticleCard {
    border: 1px solid $grey-1;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    background-color: $white;

    .Header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid $grey-1;

      .Profile {
        display: flex;
        align-items: center;

        .owner {
          display: flex;
          align-items: center;

          .ownerPic {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 10px;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .ownerAlpha {
              width: 100%;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: $success;
              color: $white;
              font-size: 20px;
              font-weight: $font-weight-medium;
            }
          }

          .ownerName {
            font-size: 16px;
            font-weight: $font-weight-medium;
            color: $title;
          }
        }
      }

      .ArticleDate {
        font-size: 14px;
        color: $text-secondary;
      }
    }

    .ArticleBody {
      padding: 25px 20px;

      .ArticleTitle {
        font-size: 28px;
        font-weight: $font-weight-semibold;
        color: $title;
        margin-bottom: 25px;

        @include media-breakpoint-down(md) {
          font-size: 24px;
        }
      }

      .ArticleContent {
        font-family: $font-family-dada-grotesk;
        font-size: 16px;
        line-height: 1.7;
        color: $text-primary;
        word-break: break-word;

        h1, h2, h3, h4, h5, h6 {
          margin-top: 1.5em;
          margin-bottom: 0.75em;
          font-weight: $font-weight-semibold;
          color: $title;
        }

        h1 {
          font-size: 26px;
        }

        h2 {
          font-size: 24px;
        }

        h3 {
          font-size: 20px;
        }

        h4 {
          font-size: 18px;
        }

        p {
          margin-bottom: 1.2em;
        }

        a {
          color: $success;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
            color: $hover-button;
          }
        }

        blockquote {
          border-left: 4px solid $success;
          padding-left: 15px;
          margin-left: 0;
          font-style: italic;
          color: $text-secondary;
        }

        ul, ol {
          margin-bottom: 1.2em;
          padding-left: 20px;

          li {
            margin-bottom: 0.5em;
          }
        }

        img {
          max-width: 100%;
          height: auto;
          margin: 1em 0;
          border-radius: 5px;
          display: block;
        }
      }

      .ArticleMediaContent {
        margin-top: 30px;

        .media-item {
          margin-bottom: 25px;

          &:last-child {
            margin-bottom: 0;
          }

          .article-image {
            img {
              width: 100%;
              max-height: 600px;
              object-fit: contain;
              border-radius: 5px;
            }
          }

          .article-video {
            width: 100%;

            video {
              width: 100%;
              border-radius: 5px;
              background-color: $grey-1;
            }
          }

          .article-audio {
            width: 100%;
            padding: 15px;
            background-color: $grey-1;
            border-radius: 5px;
          }

          .caption {
            font-size: 14px;
            color: $text-secondary;
            font-style: italic;
            margin-top: 10px;
            text-align: center;
          }
        }
      }
    }
  }
}

.article-error {
  text-align: center;
  padding: 50px 20px;

  h3 {
    color: $title;
    margin-bottom: 20px;
  }

  .back-button {
    background: $success;
    color: $white;
    border: none;
    padding: 10px 20px;
    border-radius: 30px;
    font-family: $font-family-base;
    font-weight: $font-weight-medium;
    cursor: pointer;
    display: inline-flex;
    align-items: center;

    i {
      margin-right: 8px;
    }

    &:hover {
      background-color: $hover-button;
    }
  }
}

.article-view-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px;

  .article-content {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-top: 30px;

    .article-header {
      margin-bottom: 40px;
      position: relative;

      .go-back-button {
        position: absolute;
        top: 0;
        right: 0;
        background: $success;
        color: $white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 18px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        z-index: 10;

        &:hover {
          background: $hover-button;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        &:active {
          transform: translateY(0);
        }

        i {
          font-size: 16px;
        }
      }

      h1 {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 20px;
        line-height: 1.3;
        padding-right: 60px; // Add padding to prevent overlap with button
      }

      .article-meta {
        display: flex;
        align-items: center;
        gap: 15px;

        .author-pic {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          &.author-initial {
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
          }
        }

        .author-name {
          font-size: 1.1rem;
          color: #444;
          font-weight: 500;
        }

        .article-date {
          color: #666;
          font-size: 0.9rem;
        }
      }
    }

    .article-body {
      font-size: 1.1rem;
      line-height: 1.8;
      color: #333;

      .article-description {
        margin-bottom: 2em;

        // Quill viewer styles
        .quill {
          border: none;

          .ql-toolbar {
            display: none; // Hide toolbar in read-only mode
          }

          .ql-container {
            border: none;
            font-family: inherit;
            font-size: inherit;

            .ql-editor {
              padding: 0;

              p {
                margin-bottom: 1.2em;

                &:last-child {
                  margin-bottom: 0;
                }
              }

              img {
                max-width: 100%;
                height: auto;
                margin: 1em 0;
                border-radius: 8px;
                display: block;
              }

              h1, h2, h3, h4, h5, h6 {
                margin: 1.5em 0 0.5em;
                color: #222;
                font-weight: 600;
                line-height: 1.3;
              }

              h1 { font-size: 2em; }
              h2 { font-size: 1.75em; }
              h3 { font-size: 1.5em; }
              h4 { font-size: 1.25em; }
              h5 { font-size: 1.1em; }
              h6 { font-size: 1em; }

              ul, ol {
                margin: 1em 0;
                padding-left: 2em;

                li {
                  margin-bottom: 0.5em;

                  &:last-child {
                    margin-bottom: 0;
                  }
                }
              }

              a {
                color: #06c;
                text-decoration: none;

                &:hover {
                  text-decoration: underline;
                }
              }

              blockquote {
                margin: 1.5em 0;
                padding-left: 1em;
                border-left: 4px solid #06c;
                color: #666;
                font-style: italic;
              }

              code {
                background: #f5f5f5;
                padding: 0.2em 0.4em;
                border-radius: 3px;
                font-family: monospace;
                font-size: 0.9em;
              }

              pre.ql-syntax {
                background: #23241f;
                color: #f8f8f2;
                overflow: visible;
                padding: 1em;
                border-radius: 4px;
                font-family: monospace;
              }

              // Alignment classes
              .ql-align-center {
                text-align: center;
              }

              .ql-align-right {
                text-align: right;
              }

              .ql-align-justify {
                text-align: justify;
              }

              // Text sizes
              .ql-size-small {
                font-size: 0.875em;
              }

              .ql-size-large {
                font-size: 1.5em;
              }

              .ql-size-huge {
                font-size: 2em;
              }

              // Text colors and backgrounds
              .ql-color-red { color: #e60000; }
              .ql-color-orange { color: #f90; }
              .ql-color-yellow { color: #ff0; }
              .ql-color-green { color: #008a00; }
              .ql-color-blue { color: #06c; }
              .ql-color-purple { color: #93f; }

              .ql-bg-red { background-color: #ffebeb; }
              .ql-bg-orange { background-color: #fff5eb; }
              .ql-bg-yellow { background-color: #fffbeb; }
              .ql-bg-green { background-color: #ebf7eb; }
              .ql-bg-blue { background-color: #ebf3ff; }
              .ql-bg-purple { background-color: #f5ebff; }
            }
          }
        }
      }

      .article-media-content {
        margin-top: 2em;

        .media-item {
          margin-bottom: 2em;

          &:last-child {
            margin-bottom: 0;
          }

          .article-image {
            img {
              max-width: 100%;
              height: auto;
              border-radius: 8px;
              display: block;
              margin: 0 auto;
            }
          }

          .article-video {
            video {
              width: 100%;
              border-radius: 8px;
              background: #000;
            }
          }

          .article-audio {
            padding: 20px;
            background: #f5f5f5;
            border-radius: 8px;
          }

          .caption {
            margin-top: 10px;
            text-align: center;
            font-size: 0.9em;
            color: #666;
            font-style: italic;
          }
        }
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .article-view-container {
    padding: 15px;

    .article-content {
      padding: 20px;

      .article-header {
        margin-bottom: 30px;

        .go-back-button {
          width: 45px;
          height: 45px;
          font-size: 16px;

          i {
            font-size: 14px;
          }
        }

        h1 {
          font-size: 2rem;
          padding-right: 55px; // Adjust padding for smaller button
        }
      }

      .article-body {
        font-size: 1rem;

        .article-description {
          .quill .ql-container .ql-editor {
            h1 { font-size: 1.75em; }
            h2 { font-size: 1.5em; }
            h3 { font-size: 1.35em; }
            h4 { font-size: 1.2em; }
            h5 { font-size: 1.1em; }
            h6 { font-size: 1em; }
          }
        }
      }
    }
  }
}