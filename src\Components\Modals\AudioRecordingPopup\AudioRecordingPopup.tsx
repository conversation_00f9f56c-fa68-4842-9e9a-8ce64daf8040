// @ts-nocheck
import './AudioRecordingPopup.scss'

import <PERSON><PERSON> from 'lottie-react'
import <PERSON><PERSON> from 'react-bootstrap/Modal'

import * as animationData from '../../../Assets/Lottie/audio-lottie.json'

const AudioRecordingPopup = (props: any) => {
  return (
    <>
      <Modal {...props} show={props.show} onHide={() => props.handleClose} centered className="AudioRecordingPopup">
        <Modal.Header closeButton>
          <div>
            <img src="https://i.pravatar.cc/70" alt="" />
            <p>Sending personalized audio message to Sa<PERSON></p>
          </div>
        </Modal.Header>
        <Modal.Body>
          <form className="Audio">
            {/* ---------Create Audio Starts------------ */}
            <div className="AudioPost">
              <div className="UploadSec">
                <div className="RecSec">
                  <button>
                    <i className="fa fa-butterflymic"></i> Start Recording
                  </button>
                </div>
              </div>
              <div className="UploadSec">
                <div className="Ripple">
                  <Lottie animationData={animationData} />
                </div>
                <div className="RecSec">
                  <span>
                    <i className="fa fa-stoprecording" aria-hidden="true"></i>Stop Recording
                  </span>
                </div>
              </div>
              <div className="UploadSec">
                <div className="row">
                  <div className="AudioWaves">
                    <div className="audioWrapper"></div>
                  </div>
                  <div className="audioControl ControllerIcon">
                    <i className="fa fa-butterflyaudioplay" aria-hidden="true"></i>
                  </div>
                </div>
                <div className="row">
                  <div className="delete text-center">
                    <i className="fa fa-butterflydelete" aria-hidden="true"></i>
                  </div>
                </div>
              </div>
            </div>
            {/* ---------Create Audio Ends------------ */}
          </form>
        </Modal.Body>
        <Modal.Footer>
          <button type="button">Send</button>
        </Modal.Footer>
      </Modal>
    </>
  )
}

export default AudioRecordingPopup
