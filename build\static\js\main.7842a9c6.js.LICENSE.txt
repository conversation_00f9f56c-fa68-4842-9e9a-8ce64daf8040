/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/

/*!
 * Intro.js v7.2.0
 * https://introjs.com
 *
 * Copyright (C) 2012-2023 Afshin <PERSON> (@afshinmeh).
 * https://introjs.com
 *
 * Date: Mon, 14 Aug 2023 19:47:14 GMT
 */

/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, <PERSON>
 * Copyright (c) 2013, salesforce.com
 */

/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> <https://feross.org>
 * @license  MIT
 */

/*!
 * dashify <https://github.com/jonschlinkert/dashify>
 *
 * Copyright (c) 2015-2017, <PERSON>.
 * Released under the MIT License.
 */

/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-08-28T13:37Z
 */

/*!
 * wavesurfer.js 6.6.4 (2023-06-10)
 * https://wavesurfer-js.org
 * @license BSD-3-Clause
 */

/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

/*! ./absMax */

/*! ./clamp */

/*! ./drawer */

/*! ./drawer.canvasentry */

/*! ./drawer.multicanvas */

/*! ./fetch */

/*! ./frame */

/*! ./get-id */

/*! ./max */

/*! ./mediaelement */

/*! ./mediaelement-webaudio */

/*! ./min */

/*! ./observer */

/*! ./orientation */

/*! ./peakcache */

/*! ./prevent-click */

/*! ./request-animation-frame */

/*! ./silence-mode */

/*! ./style */

/*! ./util */

/*! ./util/get-id */

/*! ./util/style */

/*! ./webaudio */

/*! debounce */

/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */

/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */

/*!***********************!*\
        !*** ./src/drawer.js ***!
        \***********************/

/*!*************************!*\
        !*** ./src/util/max.js ***!
        \*************************/

/*!*************************!*\
        !*** ./src/util/min.js ***!
        \*************************/

/*!*************************!*\
        !*** ./src/webaudio.js ***!
        \*************************/

/*!**************************!*\
        !*** ./src/peakcache.js ***!
        \**************************/

/*!***************************!*\
        !*** ./src/util/clamp.js ***!
        \***************************/

/*!***************************!*\
        !*** ./src/util/fetch.js ***!
        \***************************/

/*!***************************!*\
        !*** ./src/util/frame.js ***!
        \***************************/

/*!***************************!*\
        !*** ./src/util/index.js ***!
        \***************************/

/*!***************************!*\
        !*** ./src/util/style.js ***!
        \***************************/

/*!***************************!*\
        !*** ./src/wavesurfer.js ***!
        \***************************/

/*!****************************!*\
        !*** ./src/util/absMax.js ***!
        \****************************/

/*!****************************!*\
        !*** ./src/util/get-id.js ***!
        \****************************/

/*!*****************************!*\
        !*** ./src/mediaelement.js ***!
        \*****************************/

/*!******************************!*\
        !*** ./src/util/observer.js ***!
        \******************************/

/*!*********************************!*\
        !*** ./src/util/orientation.js ***!
        \*********************************/

/*!**********************************!*\
        !*** ./src/util/silence-mode.js ***!
        \**********************************/

/*!***********************************!*\
        !*** ./src/drawer.canvasentry.js ***!
        \***********************************/

/*!***********************************!*\
        !*** ./src/drawer.multicanvas.js ***!
        \***********************************/

/*!***********************************!*\
        !*** ./src/util/prevent-click.js ***!
        \***********************************/

/*!**************************************!*\
        !*** ./src/mediaelement-webaudio.js ***!
        \**************************************/

/*!****************************************!*\
        !*** ./node_modules/debounce/index.js ***!
        \****************************************/

/*!*********************************************!*\
        !*** ./src/util/request-animation-frame.js ***!
        \*********************************************/

/**
 * @license React
 * react-dom-server-legacy.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react-dom-server.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */

/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @param {?boolean} capture Check if the capture phase is supported.
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */

/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */

/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */

/** @license React v16.13.1
   * react-is.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

/** @license React v17.0.2
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

//! goToAndStop must be relative to the start of the current segment
