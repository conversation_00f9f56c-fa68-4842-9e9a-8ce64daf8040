.recent-media-carousel {
  margin: 20px 0;
  padding: 0;
  width: 100%;

  h4 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
    padding: 0 20px;
  }

  .media-carousel-wrapper {
    position: relative;
    padding: 0 40px;
    width: 100%;

    .control {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;

      &.prev {
        left: 10px;
      }

      &.next {
        right: 10px;
      }

      i {
        font-size: 14px;
        color: #666;
      }
    }

    > div {
      display: flex;
      justify-content: flex-start !important;
      width: 100%;
      max-width: 100% !important;
      gap: 16px;
      padding: 0 10px;
    }
  }

  .media-item {
    width: calc(16.666% - 14px) !important;
    min-width: 140px;
    max-width: 180px;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
    transition: transform 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    flex: 0 0 auto !important;
    margin-right: 16px;

    &:hover {
      transform: scale(1.05);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .video-thumbnail {
      position: relative;
      width: 100%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 30px;
        height: 30px;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: white;
          font-size: 12px;
        }
      }
    }

    .audio-thumbnail {
      width: 100%;
      height: 100%;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 24px;
        color: #666;
      }
    }
  }
}

@media (max-width: 1024px) {
  .recent-media-carousel {
    .media-item {
      width: calc(25% - 12px) !important;
      margin-right: 12px;
    }
  }
}

@media (max-width: 768px) {
  .recent-media-carousel {
    padding: 0;

    .media-carousel-wrapper {
      padding: 0 30px;

      > div {
        gap: 12px;
      }
    }

    .media-item {
      width: calc(33.333% - 8px) !important;
      min-width: 100px;
      margin-right: 12px;
    }
  }
}

@media (max-width: 480px) {
  .recent-media-carousel {
    .media-carousel-wrapper {
      > div {
        gap: 8px;
      }
    }

    .media-item {
      width: calc(33.333% - 6px) !important;
      min-width: 90px;
      margin-right: 8px;
    }
  }
}