import React, { useState } from 'react';
import './RecentPostsCarousel.scss';
import { useSelector } from 'react-redux';
import { Card } from 'react-bootstrap';
import Carousel from 'react-simply-carousel';
import { Link } from 'react-router-dom';
import { createdMonthDayYear, htmlContentWithLink, stripMarkdown } from '../../../commonservice';
import cn from 'classnames';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { addPageAction } from '../../../Redux/slices/pageDetailSlice';
import { getPostComments } from '../../../Redux/slices/post';
import { RouteConstants } from '../../../Constants/routeConstants';

interface RecentPostsCarouselProps {
  userData: any;
  pageData: any;
  canAction: () => boolean;
  setModalShow: (show: boolean) => void;
  setActivePost: (post: any) => void;
  stopAllMedia: (key: any, vindex: any, type: string) => void;
}

const RecentPostsCarousel: React.FC<RecentPostsCarouselProps> = ({
  userData,
  pageData,
  canAction,
  setModalShow,
  setActivePost,
  stopAllMedia,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [activeSlide, setActiveSlide] = useState(0);
  
  const posts = useSelector((state: any) => state?.post?.postList?.data || []);

  const recentPosts = React.useMemo(() => {
    if (!Array.isArray(posts)) return [];

    return posts
      .filter((post: any) => !post.isPinned)
      .sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10);
  }, [posts]);

  const loginRedirect = () => {
    navigate(RouteConstants.LOGIN);
  };

  const responsive = [
    { minWidth: 1024.9, itemsToShow: 3 },
    { minWidth: 768, itemsToShow: 2 },
    { maxWidth: 767, itemsToShow: 1 },
  ];

  if (!Array.isArray(recentPosts) || recentPosts.length === 0) {
    console.log('No recent posts available to display in carousel');
    return null;
  }

  const renderPostCard = (post: any, index: number) => {
    return (
      <Card className="PostCard">
        <div className="Header">
          <div className="Profile">
            <div className="owner">
              <div className="ownerPic">
                {post?.createdBy?.image ? (
                  <img src={post.createdBy.image} alt="" />
                ) : (
                  <div className="ownerAlpha">
                    <span>{post?.createdBy?.name?.charAt(0)}</span>
                  </div>
                )}
              </div>
              <div className="ownerName">{post?.createdBy?.name}</div>
            </div>
          </div>
        </div>

        <div className={post?.title === '' && post?.description === '' ? 'withoutdesc Body' : 'Body'}>
          {post?.title !== '' && post?.description !== '' && (
            <div className="wrapper">
              {post?.title && <p className="Question">{stripMarkdown(post?.title)}</p>}
              {post?.description && (
                <p className="Description">{htmlContentWithLink(stripMarkdown(post?.description))}</p>
              )}
            </div>
          )}

          {post?.medias?.length > 0 && (
            <div className="GridWrapper">
              <div className="ImageGrid">
                <div className={cn('grid-container', {
                  'single-image': post?.medias.length === 1,
                  'two-image-grid': post?.medias.length === 2,
                  'three-image-grid': post?.medias.length === 3,
                  'four-image-grid': post?.medias.length === 4,
                })}>
                  {post?.medias?.map((media: any, mediaIndex: number) => {
                    if (mediaIndex < 4) {
                      return (
                        <div
                          key={mediaIndex}
                          className={cn(`eachThumbnail eachThumbnail-${mediaIndex + 1}`, {
                            'video-type': media.type === 'video',
                            'no-video-thumb': media.type === 'video' && !media.thumbnail,
                          })}
                          style={{
                            backgroundImage: `url(${media.type === 'video' && media.thumbnail ? media.thumbnail : media.url})`
                          }}
                          onClick={() => {
                            setModalShow(true);
                            setActivePost({
                              ...post,
                              activeIndex: mediaIndex,
                            });
                          }}
                        >
                          {media.type === 'video' && (
                            <>
                              <div className="overlay"></div>
                              <i className="fa fa-play"></i>
                            </>
                          )}
                          {mediaIndex === 3 && post?.medias.length > 4 && (
                            <div className="more-images">
                              <span>+{post?.medias.length - 4}</span>
                            </div>
                          )}
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="Footer">
          <div className="FooterText">
            <p>{createdMonthDayYear(post?.createdAt)}</p>
          </div>
          {/* <div className="FooterButton">
            <span
              onClick={() => {
                if (!userData?.value?.data?._id) {
                  loginRedirect();
                } else if (!canAction()) {
                  dispatch(addPageAction('show-subsciption-step'));
                } else {
                  dispatch(getPostComments([]));
                  setModalShow(true);
                  setActivePost({
                    ...post,
                    activeIndex: null,
                  });
                }
              }}
            >
              {post?.counts?.comments ? post?.counts?.comments : ''}
              <i className={post?.counts?.comments > 0 ? 'fa fa-comment' : 'fa fa-nocomment'}></i>
            </span>
            <span>
              {post?.likes?.length ? post?.likes?.length : ''}
              <i className="fa fa-heartoutline"></i>
            </span>
          </div> */}
        </div>
      </Card>
    );
  };

  return (
    <div className="recent-posts-carousel">
      <h4>Recent Posts</h4>
      <Carousel
        containerProps={{
          style: {
            width: '100%',
            justifyContent: 'space-between',
            userSelect: 'text',
          },
          className: 'carousel-wrapper',
        }}
        infinite={false}
        activeSlideIndex={activeSlide}
        onRequestChange={setActiveSlide}
        forwardBtnProps={{
          children: <i className="fa fa-arrow-right"></i>,
          className: 'control next',
        }}
        backwardBtnProps={{
          children: <i className="fa fa-arrow-left"></i>,
          className: 'control prev',
        }}
        dotsNav={{
          show: false,
        }}
        speed={100}
        responsiveProps={responsive}
        preventScrollOnSwipe={true}
      >
        {recentPosts.map((post: any, index: number) => (
          <div
            key={index}
            className="post-item"
            onClick={() => {
              if (canAction()) {
                setModalShow(true);
                setActivePost({
                  ...post,
                  activeIndex: post.medias?.[0] ? 0 : null,
                });
              }
            }}
          >
            {post.medias?.[0] && (
              <div
                className="post-media"
                style={{
                  backgroundImage: `url(${
                    post.medias[0].type === 'video' && post.medias[0].thumbnail
                      ? post.medias[0].thumbnail
                      : post.medias[0].url
                  })`,
                }}
              >
                {post.medias[0].type === 'video' && (
                  <div className="play-icon">
                    <i className="fa fa-play"></i>
                  </div>
                )}
              </div>
            )}
            <div className="post-content">
              {post.title && <div className="post-title">{post.title}</div>}
              {post.description && <div className="post-description">{post.description}</div>}
              <div className="post-meta">
                <div className="post-date">
                  <i className="fa fa-calendar"></i>
                  {createdMonthDayYear(post.createdAt)}
                </div>
                {/* <div className="post-stats">
                  <span>
                    <i className="fa fa-comment"></i>
                    {post.counts?.comments || 0}
                  </span>
                  <span>
                    <i className="fa fa-heart"></i>
                    {post.likes?.length || 0}
                  </span>
                </div> */}
              </div>
            </div>
          </div>
        ))}
      </Carousel>
    </div>
  );
};

export default RecentPostsCarousel; 